# Development overrides for compose.yml
# Optimized for performance - increased resource allocations

services:

  # Auth Service with optimized resources for performance
  auth:
    deploy:
      resources:
        limits:
          cpus: '1.0'          # Increased from 0.25
          memory: 512M         # Increased from 192M
        reservations:
          cpus: '0.25'         # Increased from 0.05
          memory: 128M         # Increased from 64M
    volumes:
      - ./app:/app/app:rw  # Read-write for development

  # Socket Service with optimized resources for performance
  socket:
    deploy:
      resources:
        limits:
          cpus: '1.0'          # Increased from 0.25
          memory: 512M         # Increased from 192M
        reservations:
          cpus: '0.25'         # Increased from 0.05
          memory: 128M         # Increased from 64M
    volumes:
      - ./app:/app/app:rw  # Read-write for development

  # Management Service with optimized resources for performance
  management:
    deploy:
      resources:
        limits:
          cpus: '1.5'          # Higher allocation for consolidated service
          memory: 768M         # Higher memory for consolidated functionality
        reservations:
          cpus: '0.5'          # Higher reservation for consolidated service
          memory: 256M         # Higher reservation for consolidated service
    volumes:
      - ./app:/app/app:rw  # Read-write for development

  # Redis with optimized resources for performance
  redis:
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    deploy:
      resources:
        limits:
          cpus: '0.5'          # Increased from 0.2
          memory: 512M         # Increased from 128M
        reservations:
          cpus: '0.1'          # Increased from 0.05
          memory: 128M         # Increased from 32M

  # Traefik with optimized resources for performance
  traefik:
    deploy:
      resources:
        limits:
          cpus: '0.5'          # Increased from 0.2
          memory: 256M         # Increased from 64M
        reservations:
          cpus: '0.1'          # Increased from 0.05
          memory: 64M          # Increased from 32M

