"""
Task Utilities V2 for Socket Service V2

Collection-based task and story utilities that handle saving to collections
and returning collection IDs instead of individual items.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union
from bson.objectid import ObjectId
import asyncio
import uuid
import os

from google import genai
from google.genai import types
from app.shared.db_enums import TaskStatus, QuizType, InputType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
from app.v2.api.socket_service_v2.generator.prompt_maker_v2 import generate as generate_v2

# Import v1 generation functions for exact compatibility
from app.v2.api.socket_service_v2.generator.audiogen import generate_audio
from app.v2.api.socket_service_v2.generator.imagen import generate_image

# Configure logging
logger = setup_new_logging(__name__)

# Retry configuration
MAX_RETRIES = 3
INITIAL_RETRY_DELAY = 1.0
MAX_RETRY_DELAY = 30.0


def _map_task_type(prompt_maker_type: str) -> str:
    """
    Map prompt_maker.py task types to database QuizType enum values.

    Args:
        prompt_maker_type: Task type from prompt_maker.py

    Returns:
        Mapped QuizType enum value
    """
    type_mapping = {
        "single_choice": QuizType.SINGLE_CHOICE.value,
        "multiple_choice": QuizType.MULTIPLE_CHOICE.value,
        "image_identification": QuizType.IMAGE_IDENTIFICATION.value,
        "image_identify": QuizType.IMAGE_IDENTIFICATION.value,  # Keep old mapping for compatibility
        "speak_word": QuizType.SPEAK_WORD.value,
        "true_false": QuizType.SINGLE_CHOICE.value,  # Map true_false to single_choice
        "fill_in_blank": QuizType.ANSWER_IN_WORD.value,  # Map fill_in_blank to answer_in_word
        "visual_question": QuizType.IMAGE_IDENTIFICATION.value,  # Map visual_question to image_identification
        "pronunciation": QuizType.SPEAK_WORD.value,  # Map pronunciation to speak_word
        "audio_identification": QuizType.SPEAK_WORD.value,  # Map audio_identification to speak_word
    }

    return type_mapping.get(prompt_maker_type, QuizType.SINGLE_CHOICE.value)


def serialize_usage_metadata(usage_metadata: Any) -> Dict[str, Any]:
    """
    Convert usage metadata to a MongoDB-serializable dictionary.

    Args:
        usage_metadata: Usage metadata object from Gemini API

    Returns:
        Serializable dictionary
    """
    if not usage_metadata:
        return {}

    try:
        # If it has model_dump method (Pydantic model)
        if hasattr(usage_metadata, 'model_dump'):
            return usage_metadata.model_dump()
        # If it has dict method
        elif hasattr(usage_metadata, 'dict'):
            return usage_metadata.dict()
        # If it's already a dict
        elif isinstance(usage_metadata, dict):
            return usage_metadata
        # Try to convert to dict
        else:
            return dict(usage_metadata) if usage_metadata else {}
    except Exception as e:
        logger.warning(f"Failed to serialize usage metadata: {e}")
        return {"serialization_error": str(e)}


async def retry_with_exponential_backoff(func, *args, **kwargs):
    """Retry function with exponential backoff."""
    delay = INITIAL_RETRY_DELAY
    
    for attempt in range(MAX_RETRIES):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            if attempt == MAX_RETRIES - 1:
                raise e
            
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s...")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_RETRY_DELAY)


async def save_task_collection_and_items_with_priority(
    current_user: UserTenantDB,
    session_id: str,
    tasks_data: Dict[str, Any],
    collection_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Optional[Any] = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Save tasks and stories with priority-based parallel processing.

    Priority Processing:
    1. INSTANT: Text-based tasks (single_choice, multiple_choice) - saved immediately
    2. PARALLEL: Media tasks (image_identification, speak_word) - generated in background
    3. PARALLEL: Story images - generated in background with lowest priority

    Args:
        current_user: Current user context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker_v2.py with tasks and stories
        collection_id: Optional existing collection ID, creates new one if None
        audio_storage_info: Optional MinIO storage information
        socketio_server: Optional SocketIO server for real-time updates
        use_background_tasks: Whether to use background processing

    Returns:
        Dictionary with task_set_id (collection ID) and instant text tasks ready
    """
    try:
        if not tasks_data or not tasks_data.get("tasks"):
            return {
                "status": "error",
                "error": "No tasks data provided",
                "task_set_id": None
            }

        # Generate collection ID if not provided (this will be the task_set_id)
        if not collection_id:
            collection_id = str(uuid.uuid4())

        tasks = tasks_data["tasks"]
        stories = tasks_data.get("stories", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        usage_metadata = serialize_usage_metadata(tasks_data.get("usage_metadata", {}))
        title = tasks_data.get("title", "Generated Task Set V2")

        # Separate tasks by priority for parallel processing
        text_tasks = []  # Highest priority - instant
        media_tasks = []  # Medium priority - parallel background

        for task in tasks:
            task_type = task.get("type", "single_choice")
            if task_type in ["single_choice", "multiple_choice", "true_false", "fill_in_blank"]:
                text_tasks.append(task)
            else:
                media_tasks.append(task)

        logger.info(f"💾 Priority processing: {len(text_tasks)} text tasks (instant), {len(media_tasks)} media tasks (parallel), {len(stories)} stories (background)")

        # Create task set document using existing task_sets collection structure
        task_set_id = ObjectId()
        task_set_doc = {
            "_id": task_set_id,
            "user_id": ObjectId(current_user.user.id),
            "session_id": session_id,
            "title": title,
            "input_type": InputType.AUDIO,
            "tasks": [],  # Will be populated with text tasks first, then media tasks
            "stories": [],  # Will be populated with stories (images generated in background)
            "total_tasks": len(tasks),
            "total_stories": len(stories),
            "text_tasks_ready": len(text_tasks),
            "media_tasks_pending": len(media_tasks),
            "attempted_tasks": 0,
            "total_verified": 0,
            "status": TaskStatus.PENDING,
            "total_score": 0,
            "scored": 0,
            "attempts_count": 0,
            "created_at": datetime.now(timezone.utc),
            # V2 specific metadata
            "v2_collection_id": collection_id,
            "optimization_metadata": optimization_stats,
            "usage_metadata": usage_metadata,
            "service_version": "v2",
            "priority_processing": {
                "text_tasks_count": len(text_tasks),
                "media_tasks_count": len(media_tasks),
                "stories_count": len(stories),
                "processing_status": "text_ready_media_pending"
            }
        }

        # Add audio storage information if provided (same as v1)
        if audio_storage_info:
            # Store complete MinIO object information in input_content (like v1)
            input_content = {
                "object_name": audio_storage_info.get("object_name"),
                "bucket_name": audio_storage_info.get("bucket_name"),
                "object_path": audio_storage_info.get("object_path"),
                "file_name": audio_storage_info.get("file_name"),
                "content_type": audio_storage_info.get("content_type", "audio/wav"),
                "size_bytes": audio_storage_info.get("size_bytes"),
                "folder": audio_storage_info.get("folder", "recordings_v2"),
                "session_id": session_id,
                "created_at": audio_storage_info.get("created_at"),
                "file_extension": audio_storage_info.get("file_extension", ".wav"),
            }
            task_set_doc["input_content"] = input_content
            logger.debug(f"Adding input_content to task set (same as v1)")
        else:
            logger.debug("No audio_storage_info provided, input_content will not be added")

        # PRIORITY 1: Process text-based tasks INSTANTLY (no media needed)
        total_score = 0
        instant_tasks = []
        pending_media_tasks = []

        # Process text tasks first - these go to task_items collection immediately
        for task_data in text_tasks:
            try:
                task_item_id = ObjectId()
                task_type = _map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "instant",
                        "_media_ready": True
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error processing instant task: {e}")
                continue

        # Save instant text tasks to task_items collection immediately
        if instant_tasks:
            await current_user.async_db.task_items.insert_many(instant_tasks)
            logger.info(f"✅ Saved {len(instant_tasks)} instant text tasks to task_items collection")

        # PRIORITY 2: Save media tasks to database immediately (media will be generated in background)
        for task_data in media_tasks:
            try:
                task_item_id = ObjectId()
                task_type = _map_task_type(task_data.get("type", "single_choice"))
                question_data = task_data.get("question", {})

                task_item = {
                    "_id": task_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "type": task_type,
                    "title": task_data.get("title", "Generated Task"),
                    "question": {
                        "text": question_data.get("text", ""),
                        "translated_text": question_data.get("translated_text", ""),
                        "options": question_data.get("options", {}),
                        "answer_hint": question_data.get("answer_hint", ""),
                        "metadata": question_data.get("metadata", {})
                    },
                    "correct_answer": {
                        "value": question_data.get("answer", ""),
                        "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                               "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                    },
                    "user_answer": None,
                    "status": TaskStatus.PENDING.value,
                    "result": None,
                    "remark": None,
                    "total_score": task_data.get("total_score", 10),
                    "scored": 0,
                    "submitted": False,
                    "submitted_at": None,
                    "attempts_count": 0,
                    "difficulty_level": task_data.get("difficulty_level", 2),
                    "metadata": {
                        "_v2_optimized": task_data.get("_v2_optimized", False),
                        "_media_excluded": task_data.get("_media_excluded", False),
                        "_priority": "media_pending",
                        "_media_ready": False
                    },
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                pending_media_tasks.append(task_item)
                total_score += task_item["total_score"]

            except Exception as e:
                logger.error(f"Error preparing media task: {e}")
                continue

        # Save media tasks to task_items collection immediately (media will be generated in background)
        if pending_media_tasks:
            await current_user.async_db.task_items.insert_many(pending_media_tasks)
            logger.info(f"✅ Saved {len(pending_media_tasks)} media tasks to task_items collection (media pending)")

        # PRIORITY 3: Process stories for background image generation
        instant_stories = []
        for story_data in stories:
            try:
                story_item_id = ObjectId()
                story_item = {
                    "_id": story_item_id,
                    "task_set_id": task_set_id,
                    "user_id": ObjectId(current_user.user.id),
                    "session_id": session_id,
                    "stage": story_data.get("stage", 1),
                    "script": story_data.get("script", ""),
                    "image": story_data.get("image", ""),
                    "metadata": {
                        **story_data.get("metadata", {}),
                        "_priority": "background_image",
                        "_image_ready": False
                    },
                    "task_title": story_data.get("task_title", ""),
                    "task_type": story_data.get("task_type", ""),
                    "created_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }

                instant_stories.append(story_item)

            except Exception as e:
                logger.error(f"Error processing story: {e}")
                continue

        # Save instant stories to story_steps collection (images will be generated in background)
        if instant_stories:
            await current_user.async_db.story_steps.insert_many(instant_stories)
            logger.info(f"✅ Saved {len(instant_stories)} stories to story_steps collection (images pending)")

        # Update task set with references and metadata
        all_processed_tasks = instant_tasks + pending_media_tasks
        task_set_doc["tasks"] = [{"task_item_id": str(task["_id"]), "status": task["metadata"]["_priority"]} for task in all_processed_tasks]
        task_set_doc["stories"] = [{"story_step_id": str(story["_id"]), "status": story["metadata"]["_priority"]} for story in instant_stories]
        task_set_doc["total_score"] = total_score

        # Save task set to existing task_sets collection
        await current_user.async_db.task_sets.insert_one(task_set_doc)

        # Start background processing for media tasks and story images
        if use_background_tasks and (pending_media_tasks or instant_stories):
            try:
                # Create background task with proper error handling
                background_task = asyncio.create_task(_process_media_in_background(
                    current_user, task_set_id, pending_media_tasks, instant_stories, socketio_server
                ))
                # Add callback to log any exceptions
                background_task.add_done_callback(
                    lambda task: logger.error(f"❌ Background task failed: {task.exception()}")
                    if task.exception() else logger.debug(f"✅ Background task completed successfully")
                )
                logger.info(f"🔄 Started background processing for {len(pending_media_tasks)} media tasks and {len(instant_stories)} story images")
            except Exception as e:
                logger.error(f"❌ Failed to start background processing: {e}")
                # Continue without background processing
                pass

        logger.info(f"✅ Instant response ready: {len(instant_tasks)} text tasks available immediately")

        return {
            "status": "success",
            "task_set_id": collection_id,  # Return collection ID (same as v2_collection_id)
            "collection_metadata": {
                "total_task_sets": 1,
                "total_tasks": len(all_processed_tasks),
                "total_stories": len(instant_stories),
                "instant_tasks_ready": len(instant_tasks),
                "media_tasks_pending": len(pending_media_tasks),
                "stories_pending_images": len(instant_stories),
                "optimization_stats": optimization_stats,
                "actual_task_set_id": str(task_set_id),
                "service_version": "v2",
                "priority_processing": True
            },
            "instant_tasks": instant_tasks,  # Ready immediately
            "pending_media_tasks": len(pending_media_tasks),  # Being processed in background
            "pending_stories": len(instant_stories)  # Images being generated in background
        }

    except Exception as e:
        logger.error(f"❌ Error saving task collection: {e}")
        return {
            "status": "error",
            "error": str(e),
            "task_set_id": None
        }


async def _process_media_in_background(
    current_user: UserTenantDB,
    task_set_id: ObjectId,
    pending_media_tasks: List[Dict[str, Any]],
    pending_stories: List[Dict[str, Any]],
    socketio_server: Optional[Any] = None
):
    """
    Process media tasks and story images in parallel background.

    Priority:
    1. PARALLEL: Generate media for tasks (medium priority)
    2. PARALLEL: Generate images for stories (lowest priority)
    """
    try:
        logger.info(f"🔄 Background processing started for task_set {task_set_id} with {len(pending_media_tasks)} media tasks and {len(pending_stories)} stories")

        # Log task details for debugging
        for i, task in enumerate(pending_media_tasks):
            logger.debug(f"📋 Media task {i+1}: {task.get('title', 'Unknown')} ({task.get('type', 'Unknown')})")

        for i, story in enumerate(pending_stories):
            logger.debug(f"📖 Story {i+1}: Stage {story.get('stage', 'Unknown')}")

        # Create parallel tasks for media generation
        background_tasks = []

        # PRIORITY 2: Media tasks (parallel processing)
        if pending_media_tasks:
            for task_item in pending_media_tasks:
                background_tasks.append(
                    _generate_task_media(current_user, task_item, socketio_server)
                )

        # PRIORITY 3: Story images (parallel processing, lowest priority)
        if pending_stories:
            for story_item in pending_stories:
                background_tasks.append(
                    _generate_story_image(current_user, story_item, socketio_server)
                )

        # Execute all media generation in parallel with proper error handling
        if background_tasks:
            results = await asyncio.gather(*background_tasks, return_exceptions=True)

            # Count successful vs failed operations
            successful = sum(1 for r in results if not isinstance(r, Exception))
            failed = len(results) - successful

            if failed > 0:
                logger.warning(f"⚠️ Background processing completed with {failed} failures out of {len(results)} tasks")
            else:
                logger.info(f"✅ Background processing completed successfully for all {len(results)} tasks")

            # Log any specific failures
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"❌ Background task {i} failed: {result}")

        # Update task set status to indicate all media is ready
        try:
            await current_user.async_db.task_sets.update_one(
                {"_id": task_set_id},
                {
                    "$set": {
                        "priority_processing.processing_status": "all_media_ready",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Updated task_set {task_set_id} status to all_media_ready")
        except Exception as db_error:
            logger.error(f"❌ Failed to update task_set status: {db_error}")

        # Notify via WebSocket if available
        if socketio_server:
            try:
                await socketio_server.emit_to_user(
                    str(current_user.user.id),
                    "media_processing_complete",
                    {
                        "task_set_id": str(task_set_id),
                        "status": "all_media_ready",
                        "timestamp": datetime.now().isoformat()
                    }
                )
                logger.info(f"✅ Sent WebSocket notification for task_set {task_set_id}")
            except Exception as ws_error:
                logger.error(f"❌ Failed to send WebSocket notification: {ws_error}")

    except Exception as e:
        logger.error(f"❌ Background processing failed for task_set {task_set_id}: {e}")


async def _generate_task_media(
    current_user: UserTenantDB,
    task_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate actual media for a single task and update task_items collection."""
    try:
        task_id = task_item["_id"]
        task_type = str(task_item["type"])
        question = task_item.get("question", {})

        logger.info(f"🎨 Generating media for task {task_id} ({task_type})")

        media_url = None

        # Generate media based on task type - use exact same v1 functions
        if task_type in ["image_identification", "visual_question"]:
            # Generate image for visual tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                media_url = await _generate_image_for_task_v1(current_user, keyword, task_id)

        elif task_type in ["speak_word", "pronunciation", "audio_identification"]:
            # Generate audio for audio tasks
            keyword = question.get("answer_hint") or question.get("answer") or task_item.get("title", "")
            if keyword:
                media_url = await _generate_audio_for_task_v1(current_user, keyword, task_id)

        # Update task_items collection with generated media
        if media_url:
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata._media_ready": True,
                        "metadata._priority": "media_ready",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Media generated for task {task_id}: {media_url}")
        else:
            # Mark as ready even if no media was generated
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "metadata._media_ready": True,
                        "metadata._priority": "no_media_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Task {task_id} marked as ready (no media needed)")

        # Notify via WebSocket if available
        if socketio_server:
            await socketio_server.emit_to_user(
                str(current_user.user.id),
                "task_media_ready",
                {
                    "task_id": str(task_id),
                    "task_type": task_type,
                    "media_url": media_url,
                    "timestamp": datetime.now().isoformat()
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to generate media for task {task_item.get('_id')}: {e}")


async def _generate_image_for_task_v1(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId
) -> Optional[str]:
    """Generate image using exact v1 function and save metadata to task item."""
    try:
        logger.info(f"🎨 Generating image for keyword using v1 function: {keyword}")

        # Use exact v1 function
        file_text, file_info, usage_metadata = await generate_image(current_user, keyword)

        if not file_info:
            logger.error(f"No image file_info received for: {keyword}")
            return None

        # Save complete metadata to task item (same as v1)
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "question.metadata": file_info,  # Save complete metadata like v1
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"✅ Image generated and saved for task {task_id} using v1 function")
        return file_info.get("url") if file_info else None

    except Exception as e:
        logger.error(f"❌ Failed to generate image using v1 function for {keyword}: {e}")
        return None


async def _generate_audio_for_task_v1(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId
) -> Optional[str]:
    """Generate audio using exact v1 function and save metadata to task item."""
    try:
        logger.info(f"🔊 Generating audio for keyword using v1 function: {keyword}")

        # Use exact v1 function
        file_text, file_info, usage_metadata = await generate_audio(current_user, keyword)

        if not file_info:
            logger.error(f"No audio file_info received for: {keyword}")
            return None

        # Save complete metadata to task item (same as v1)
        await current_user.async_db.task_items.update_one(
            {"_id": task_id},
            {
                "$set": {
                    "question.metadata": file_info,  # Save complete metadata like v1
                    "updated_at": datetime.now(timezone.utc),
                    "usage": usage_metadata
                }
            }
        )

        logger.info(f"✅ Audio generated and saved for task {task_id} using v1 function")
        return file_info.get("url") if file_info else None

    except Exception as e:
        logger.error(f"❌ Failed to generate audio using v1 function for {keyword}: {e}")
        return None


async def _generate_image_for_task(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId
) -> Optional[str]:
    """Generate image using Gemini 2.0 Flash and save to MinIO."""
    try:
        logger.info(f"🎨 Generating image for keyword: {keyword}")

        # Get image prompt from database
        prompt_data = await current_user.async_db.prompts.find_one({"name": "imagen_prompt"})
        if not prompt_data:
            # Fallback prompt
            prompt_template = "Generate a clear, educational image for children showing: {keyword}. The image should be colorful, simple, and appropriate for learning."
        else:
            prompt_template = prompt_data.get("prompt", "Generate an image for: {keyword}")

        prompt = prompt_template.format(keyword=keyword)

        client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))
        model = "gemini-2.0-flash-preview-image-generation"

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            response_modalities=["IMAGE", "TEXT"],
            response_mime_type="text/plain",
        )

        file_bytes = b""

        # Generate image
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            for part in chunk.candidates[0].content.parts:
                if part.inline_data and part.inline_data.data:
                    file_bytes += part.inline_data.data

        if not file_bytes:
            logger.error(f"No image data received for: {keyword}")
            return None

        # Save to MinIO
        from app.shared.async_minio_client import create_async_minio_client

        async_minio_client = create_async_minio_client(current_user.minio)
        file_info = await async_minio_client.save_file_async(
            data=file_bytes,
            user_id=current_user.user.id,
            content_type="image/jpeg",
            folder="task_images",
            file_extension=".jpg",
            custom_filename=f"task_{task_id}_{keyword[:20]}.jpg"
        )

        logger.info(f"✅ Image generated and saved for task {task_id}")

        # Save complete metadata to task item (like v1)
        if file_info:
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,  # Save complete metadata like v1
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

        return file_info.get("url") if file_info else None

    except Exception as e:
        logger.error(f"❌ Failed to generate image for {keyword}: {e}")
        return None


async def _generate_audio_for_task(
    current_user: UserTenantDB,
    keyword: str,
    task_id: ObjectId
) -> Optional[str]:
    """Generate audio using Gemini and save to MinIO."""
    try:
        logger.info(f"🔊 Generating audio for keyword: {keyword}")

        # Get audio prompt from database
        prompt_data = await current_user.async_db.prompts.find_one({"name": "audio_prompt"})
        if not prompt_data:
            # Fallback prompt
            prompt_template = """You will be given one or more Nepali words or sentences written in Devanagari script. Your task is to generate clear and natural-sounding audio in native Nepali.

Here are the inputs:
{keyword}

Please generate clear, natural Nepali pronunciation audio for the above text."""
        else:
            prompt_template = prompt_data.get("prompt", "Generate audio for: {keyword}")

        prompt = prompt_template.format(keyword=keyword)

        client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))
        model = "gemini-2.5-flash-preview-tts"

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            temperature=1,
            response_modalities=["audio"],
            speech_config=types.SpeechConfig(
                voice_config=types.VoiceConfig(
                    prebuilt_voice_config=types.PrebuiltVoiceConfig(voice_name="Sulafat")
                )
            ),
        )

        file_bytes = b""

        # Generate audio
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            for part in chunk.candidates[0].content.parts:
                if part.inline_data and part.inline_data.data:
                    file_bytes += part.inline_data.data

        if not file_bytes:
            logger.error(f"No audio data received for: {keyword}")
            return None

        # Determine file extension and content type (same logic as v1)
        if file_bytes.startswith(b'RIFF'):
            file_extension = ".wav"
            content_type = "audio/wav"
        else:
            file_extension = ".mp3"
            content_type = "audio/mpeg"

        # Save to MinIO
        from app.shared.async_minio_client import create_async_minio_client

        async_minio_client = create_async_minio_client(current_user.minio)
        file_info = await async_minio_client.save_file_async(
            data=file_bytes,
            user_id=current_user.user.id,
            content_type=content_type,
            folder="task_audio",
            file_extension=file_extension,
            custom_filename=f"task_{task_id}_{keyword[:20]}{file_extension}"
        )

        logger.info(f"✅ Audio generated and saved for task {task_id}")

        # Save complete metadata to task item (like v1)
        if file_info:
            await current_user.async_db.task_items.update_one(
                {"_id": task_id},
                {
                    "$set": {
                        "question.metadata": file_info,  # Save complete metadata (same as images)
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

        return file_info.get("url") if file_info else None

    except Exception as e:
        logger.error(f"❌ Failed to generate audio for {keyword}: {e}")
        return None


async def _generate_story_image(
    current_user: UserTenantDB,
    story_item: Dict[str, Any],
    socketio_server: Optional[Any] = None
):
    """Generate actual image for a single story and update story_steps collection."""
    try:
        story_id = story_item["_id"]
        stage = story_item["stage"]
        image_description = story_item.get("image", "")

        logger.info(f"🖼️ Generating image for story {story_id} stage {stage}")

        image_url = None

        # Generate image if description is available
        file_info = None
        if image_description:
            image_url, file_info = await _generate_image_for_story(current_user, image_description, story_id, stage)

        # Update story_steps collection with generated image and complete metadata
        if image_url and file_info:
            # Merge file_info with story-specific metadata
            complete_metadata = {
                **file_info,  # Complete metadata from MinIO
                "_image_ready": True,
                "_priority": "image_ready"
            }

            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata": complete_metadata,  # Save complete metadata like v1 (no media_url needed)
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Image generated for story {story_id}: {image_url}")
        else:
            # Mark as ready even if no image was generated
            await current_user.async_db.story_steps.update_one(
                {"_id": story_id},
                {
                    "$set": {
                        "metadata._image_ready": True,
                        "metadata._priority": "no_image_needed",
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Story {story_id} marked as ready (no image needed)")

        # Notify via WebSocket if available
        if socketio_server:
            await socketio_server.emit_to_user(
                str(current_user.user.id),
                "story_image_ready",
                {
                    "story_id": str(story_id),
                    "stage": stage,
                    "image_url": image_url,
                    "timestamp": datetime.now().isoformat()
                }
            )

    except Exception as e:
        logger.error(f"❌ Failed to generate image for story {story_item.get('_id')}: {e}")


async def _generate_image_for_story(
    current_user: UserTenantDB,
    image_description: str,
    story_id: ObjectId,
    stage: int
) -> Optional[str]:
    """Generate image for story using Gemini 2.0 Flash and save to MinIO."""
    try:
        logger.info(f"🎨 Generating story image for: {image_description}")

        # Get story image prompt from database
        prompt_data = await current_user.async_db.prompts.find_one({"name": "story_imagen_prompt"})
        if not prompt_data:
            # Fallback prompt for story images
            prompt_template = "Generate a beautiful, child-friendly illustration for a Nepali children's story. The image should be colorful, engaging, and culturally appropriate. Description: {image_description}"
        else:
            prompt_template = prompt_data.get("prompt", "Generate a story image for: {image_description}")

        prompt = prompt_template.format(image_description=image_description)

        client = genai.Client(api_key=os.environ.get("GEMINI_API_KEY"))
        model = "gemini-2.0-flash-preview-image-generation"

        contents = [
            types.Content(
                role="user",
                parts=[types.Part.from_text(text=prompt)],
            ),
        ]

        generate_content_config = types.GenerateContentConfig(
            response_modalities=["IMAGE", "TEXT"],
            response_mime_type="text/plain",
        )

        file_bytes = b""

        # Generate image
        for chunk in client.models.generate_content_stream(
            model=model,
            contents=contents,
            config=generate_content_config,
        ):
            if (
                chunk.candidates is None
                or chunk.candidates[0].content is None
                or chunk.candidates[0].content.parts is None
            ):
                continue

            for part in chunk.candidates[0].content.parts:
                if part.inline_data and part.inline_data.data:
                    file_bytes += part.inline_data.data

        if not file_bytes:
            logger.error(f"No image data received for story: {image_description}")
            return None

        # Save to MinIO
        from app.shared.async_minio_client import create_async_minio_client

        async_minio_client = create_async_minio_client(current_user.minio)
        file_info = await async_minio_client.save_file_async(
            data=file_bytes,
            user_id=current_user.user.id,
            content_type="image/jpeg",
            folder="story_images",
            file_extension=".jpg",
            custom_filename=f"story_{story_id}_stage_{stage}.jpg"
        )

        logger.info(f"✅ Story image generated and saved for story {story_id} stage {stage}")
        return (file_info.get("url") if file_info else None), file_info

    except Exception as e:
        logger.error(f"❌ Failed to generate story image for {image_description}: {e}")
        return None, None


async def _create_or_update_task_collection(
    current_user: UserTenantDB,
    collection_id: str,
    task_set_id: str,
    session_id: str,
    optimization_stats: Dict[str, Any],
    usage_metadata: Dict[str, Any],
    audio_storage_info: Optional[Dict[str, Any]] = None
):
    """Create or update task collection document."""
    try:
        # Check if collection already exists
        existing_collection = await current_user.async_db.task_collections.find_one(
            {"collection_id": collection_id}
        )

        if existing_collection:
            # Update existing collection
            await current_user.async_db.task_collections.update_one(
                {"collection_id": collection_id},
                {
                    "$push": {"task_set_ids": task_set_id},
                    "$inc": {
                        "total_task_sets": 1,
                        "total_tasks": optimization_stats.get("total_tasks", 0)
                    },
                    "$set": {
                        "updated_at": datetime.now(timezone.utc),
                        "optimization_metadata": optimization_stats,
                        "usage_metadata": usage_metadata
                    }
                }
            )
            logger.info(f"Updated existing task collection {collection_id}")
        else:
            # Create new collection
            collection_doc = {
                "_id": ObjectId(),
                "collection_id": collection_id,
                "user_id": str(current_user.user.id),
                "session_id": session_id,
                "task_set_ids": [task_set_id],
                "input_type": InputType.AUDIO,
                "input_metadata": audio_storage_info,
                "total_task_sets": 1,
                "total_tasks": optimization_stats.get("total_tasks", 0),
                "status": TaskStatus.PENDING,
                "created_at": datetime.now(timezone.utc),
                "optimized_for_performance": True,
                "media_excluded_count": optimization_stats.get("media_excluded_count", 0),
                "optimization_metadata": optimization_stats,
                "generation_metadata": {
                    "usage_metadata": usage_metadata,
                    "version": "v2"
                }
            }
            
            await current_user.async_db.task_collections.insert_one(collection_doc)
            logger.info(f"Created new task collection {collection_id}")

    except Exception as e:
        logger.error(f"Error creating/updating task collection: {e}")
        raise


async def process_audio_with_prompt_maker_v2(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4
) -> Dict[str, Any]:
    """
    Process audio with the optimized V2 prompt maker.
    
    Args:
        current_user: Current user context
        audio_bytes: Audio data to process
        num_tasks: Number of tasks to generate
        
    Returns:
        Parsed tasks data from prompt_maker_v2.py
    """
    try:
        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker V2")

        # Call the V2 prompt maker with retry logic
        result = await retry_with_exponential_backoff(
            generate_v2,
            audio_bytes,
            num_tasks,
            current_user
        )
        
        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker_v2.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "optimization_stats": {},
                "usage_metadata": {}
            }

        logger.info(f"✅ Generated {len(result['tasks'])} tasks with V2 optimizations")
        return result

    except Exception as e:
        logger.error(f"❌ Error processing audio with prompt_maker V2: {e}")
        return {
            "tasks": [],
            "error": str(e),
            "status": "error",
            "optimization_stats": {},
            "usage_metadata": {}
        }


# Removed process_audio_with_story_generator_v2 function
# Stories are now generated together with tasks in process_audio_with_prompt_maker_v2


def convert_to_socketio_format_v2(tasks_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert V2 tasks data to Socket.IO format.
    
    This maintains compatibility with existing Socket.IO clients while
    including V2 optimization metadata.
    """
    try:
        tasks = tasks_data.get("tasks", [])
        optimization_stats = tasks_data.get("optimization_stats", {})
        
        socketio_tasks = []
        for task in tasks:
            socketio_task = {
                "id": str(task.get("id", ObjectId())),
                "type": task.get("type", "single_choice"),
                "title": task.get("title", "Generated Task"),
                "question": task.get("question", {}),
                "total_score": task.get("total_score", 10),
                "difficulty_level": task.get("difficulty_level", 2),
                "status": task.get("status", "pending"),
                # V2 specific fields
                "_v2_optimized": task.get("_v2_optimized", False),
                "_media_excluded": task.get("_media_excluded", False)
            }
            
            # Add story if present and not optimized away
            if task.get("story") and not task.get("_media_excluded", False):
                socketio_task["story"] = task["story"]
            
            socketio_tasks.append(socketio_task)
        
        logger.info(f"Converted {len(socketio_tasks)} tasks to Socket.IO format with V2 optimizations")
        return socketio_tasks
        
    except Exception as e:
        logger.error(f"Error converting tasks to Socket.IO format: {e}")
        return []


# Alias for backward compatibility
save_task_collection_and_items = save_task_collection_and_items_with_priority
