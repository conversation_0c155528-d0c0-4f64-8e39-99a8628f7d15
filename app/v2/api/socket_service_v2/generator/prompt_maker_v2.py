"""
Optimized Prompt Maker V2 for Socket Service V2

This module provides an optimized version of the prompt maker that generates tasks
with performance optimizations:
- Choice-based questions (single_choice, multiple_choice) exclude images and audio
- Only interactive tasks (speak_word, story-based) include media
- Faster processing for text-only questions
- Same generation logic as original but with media optimization
"""

import os
import json
from typing import List, Dict, Any
from datetime import datetime, timezone
from google import genai
from google.genai import types
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from bson import ObjectId

# Configure logging
logger = setup_new_logging(__name__)

# Task types that should exclude media for performance optimization
MEDIA_EXCLUDED_TASK_TYPES = {
    'single_choice',
    'multiple_choice',
    'true_false',
    'fill_in_blank'
}

# Task types that should include media for interactivity
MEDIA_INCLUDED_TASK_TYPES = {
    'speak_word',
    'story_based',
    'audio_response',
    'pronunciation'
}


async def generate_nepali_quiz_prompt_v2(num_tasks, current_user: UserTenantDB = None):
    """
    Generate optimized Nepali quiz prompt for V2.
    
    This version includes instructions for media optimization based on task types.
    """
    try:
        # Fetch onboarding data age, topic and level from user onboarding
        prompt_data = await current_user.async_db.users.find_one(
            {"_id": ObjectId(current_user.user.id)}, 
            {"age": 1, "difficulty_level": 1, "preferred_topics": 1}
        )
        logger.info(f"Prompt data: {prompt_data}")
        
        difficulty_prompt = await current_user.async_db.prompts.find_one(
            {"name": "difficulty_guidance", "level": prompt_data.get("difficulty_level")}, 
            {"prompt": 1}
        )
        
        if prompt_data.get("preferred_topics"):
            topic_prompt = await current_user.async_db.prompts.find_one(
                {"name": "topic_guidance", "topic": "existing"}, 
                {"prompt": 1}
            )
            if topic_prompt:
                topic_prompt = topic_prompt.get("prompt", "").format(topic=prompt_data.get("preferred_topics"))
        else:
            topic_prompt = await current_user.async_db.prompts.find_one(
                {"name": "topic_guidance", "topic": "non_existing"}, 
                {"prompt": 1}
            )

        main_prompt = await current_user.async_db.prompts.find_one({"name": "main_prompt"})

        # Get the prompt text from main_prompt document
        prompt_text = main_prompt.get("prompt", "") if main_prompt else ""

        # Handle topic_prompt - it could be a string (formatted) or dict (from DB)
        if isinstance(topic_prompt, str):
            topic_text = topic_prompt
        else:
            topic_text = topic_prompt.get("prompt", "") if topic_prompt else ""

        # Add V2 optimization instructions
        optimization_instructions = """

PERFORMANCE OPTIMIZATION INSTRUCTIONS FOR V2:
- For single_choice, multiple_choice, true_false, and fill_in_blank tasks: DO NOT include media_url, images, or audio references
- For speak_word, story_based, audio_response, and pronunciation tasks: Include media_url and relevant media
- Prioritize text-based questions for faster processing
- Only include media when it's essential for the task type
"""

        formatted_prompt = prompt_text.format(
            num_tasks=num_tasks,
            difficulty_level=prompt_data.get("difficulty_level"),
            age=prompt_data.get("age"),
            topic_prompt=topic_text,
            difficulty_prompt=difficulty_prompt.get("prompt", "") if difficulty_prompt else ""
        ) + optimization_instructions

        return formatted_prompt.strip()
    except Exception as e:
        import traceback
        print(traceback.print_exc())
        print(f"Error generating prompt: {e}")
        raise


def optimize_task_for_performance(task: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize a single task for performance by removing unnecessary media.
    
    Args:
        task: Task dictionary from Gemini response
        
    Returns:
        Optimized task dictionary
    """
    task_type = task.get('type', 'single_choice')
    
    # If task type should exclude media, remove media fields
    if task_type in MEDIA_EXCLUDED_TASK_TYPES:
        # Remove media from question
        if 'question' in task:
            task['question'].pop('media_url', None)
            task['question'].pop('image_url', None)
            task['question'].pop('audio_url', None)
        
        # Remove media from story if present
        if 'story' in task:
            task['story'].pop('media_url', None)
            task['story'].pop('image', None)
            task['story'].pop('audio_url', None)
        
        # Mark as optimized
        task['_optimized'] = True
        task['_media_excluded'] = True
        
        logger.debug(f"Optimized task '{task.get('title', 'Unknown')}' by removing media")
    
    elif task_type in MEDIA_INCLUDED_TASK_TYPES:
        # Keep media for interactive tasks
        task['_optimized'] = False
        task['_media_excluded'] = False
        
        logger.debug(f"Preserved media for interactive task '{task.get('title', 'Unknown')}'")
    
    return task


def convert_to_task_items_v2(gemini_tasks: List[Dict[str, Any]], user_difficulty_level: int = None) -> Dict[str, Any]:
    """
    Convert Gemini task format to expected task item format for task_utils_v2.py.

    This V2 version includes performance optimization, tracking, and separates stories from tasks.
    """
    task_items = []
    story_items = []
    optimization_stats = {
        'total_tasks': len(gemini_tasks),
        'media_excluded_count': 0,
        'media_included_count': 0,
        'optimization_applied': True,
        'stories_extracted': 0
    }

    # Use user difficulty level as integer directly (1=easy, 2=medium, 3=hard)
    difficulty_level_int = user_difficulty_level if user_difficulty_level in [1, 2, 3] else 2

    for task in gemini_tasks:
        try:
            # Apply performance optimization first
            optimized_task = optimize_task_for_performance(task)
            
            # Track optimization stats
            if optimized_task.get('_media_excluded', False):
                optimization_stats['media_excluded_count'] += 1
            else:
                optimization_stats['media_included_count'] += 1

            # Extract basic task info
            title = optimized_task.get('title', 'Generated Task Item')
            task_type = optimized_task.get('type', 'single_choice')
            question_data = optimized_task.get('question', {})
            story_data = optimized_task.get('story', {})
            total_score = optimized_task.get('total_score', optimized_task.get('max_score', 10))
            complexity = optimized_task.get('complexity', difficulty_level_int)

            # Keep options as dictionary format
            options = question_data.get('options', {})
            if not isinstance(options, dict):
                options = {}

            # Create task item structure
            task_item = {
                "type": task_type,
                "title": title,
                "question": {
                    "text": question_data.get('text', ''),
                    "translated_text": question_data.get('translated_text', ''),
                    "options": options,
                    "answer_hint": question_data.get('answer_hint', ''),
                    "answer": question_data.get('answer', ''),
                    "media_url": question_data.get('media_url')  # Will be None if optimized
                },
                "total_score": total_score,
                "difficulty_level": difficulty_level_int,
                "complexity": complexity,
                "answer": None,
                "scored": 0,
                # V2 optimization metadata
                "_v2_optimized": optimized_task.get('_optimized', False),
                "_media_excluded": optimized_task.get('_media_excluded', False)
            }

            # Extract story data separately if present
            if story_data:
                story_item = {
                    "id": ObjectId(),
                    "stage": story_data.get('stage', 1),
                    "script": story_data.get('script', ''),
                    "image": story_data.get('image', ''),
                    "media_url": story_data.get('media_url', ''),
                    "metadata": story_data.get('metadata', {}),
                    "task_title": title,  # Link to related task
                    "task_type": task_type,
                    "created_at": datetime.now(timezone.utc)
                }

                # Apply media optimization to stories too
                if optimized_task.get('_media_excluded', False):
                    story_item["media_url"] = None
                    story_item["image"] = None

                story_items.append(story_item)
                optimization_stats['stories_extracted'] += 1

            # Handle special cases for different task types
            if task_type == 'speak_word':
                if not task_item['question']['text']:
                    task_item['question']['text'] = ''
                task_item['question']['answer'] = question_data.get('answer_hint', '')

            task_items.append(task_item)

        except Exception as e:
            logger.error(f"Error converting task: {e}")
            continue

    logger.info(f"V2 Optimization Stats: {optimization_stats}")

    return {
        "tasks": task_items,
        "stories": story_items,
        "optimization_stats": optimization_stats
    }


async def generate(audio_bytes, num_tasks, current_user: UserTenantDB = None) -> Dict[str, Any]:
    """
    Generate optimized tasks from audio and return result with optimization metadata.
    
    This V2 version includes performance optimizations and detailed tracking.
    """
    try:
        # Check if API key is available
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            logger.error("❌ GEMINI_API_KEY not found in environment variables")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}

        client = genai.Client(api_key=api_key)

        # Generate optimized prompt
        prompt_text = await generate_nepali_quiz_prompt_v2(
            num_tasks=num_tasks,
            current_user=current_user
        )
        logger.info(f"📝 Generated optimized V2 prompt for {num_tasks} tasks")

        model = "gemini-2.0-flash"
        contents = [
            types.Content(
                role="user",
                parts=[
                    types.Part.from_bytes(
                        mime_type="audio/ogg",
                        data=audio_bytes,
                    ),
                ],
            ),
        ]
        generate_content_config = types.GenerateContentConfig(
            temperature=0,
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text=prompt_text),
            ],
        )

        logger.info(f"🤖 Calling Gemini API with {len(audio_bytes)} bytes of audio (V2 optimized)")

        # Collect all chunks from the streaming response
        full_response = ""
        chunk_count = 0
        usage_metadata = {}
        
        try:
            for chunk in client.models.generate_content_stream(
                model=model,
                contents=contents,
                config=generate_content_config,
            ):
                if chunk.text:
                    full_response += chunk.text
                    chunk_count += 1
                if chunk.usage_metadata:
                    usage_metadata = chunk.usage_metadata

            logger.info(f"📥 Received {chunk_count} chunks from Gemini API (V2), total response length: {len(full_response)}")
            
            if usage_metadata:
                logger.info(f"📊 V2 Audio processing metadata: {usage_metadata}")

        except Exception as api_error:
            logger.error(f"❌ Gemini API call failed (V2): {str(api_error)}")
            import traceback
            logger.error(f"API Error traceback: {traceback.format_exc()}")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}

        # Parse and optimize the response
        if not full_response:
            logger.error("❌ Empty response from Gemini API (V2)")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

        try:
            # Get user's difficulty level
            user_profile = await current_user.async_db.users.find_one(
                {"_id": ObjectId(current_user.user.id)},
                {"difficulty_level": 1}
            )
            user_difficulty_level = user_profile.get("difficulty_level") if user_profile else 2

            # Parse response using original function
            from app.v1.api.socket_service.generator.prompt_maker import parse_gemini_response
            title, gemini_tasks = parse_gemini_response(full_response)
            logger.info(f"📋 Parsed {len(gemini_tasks)} tasks from Gemini response with title: {title}")

            if not gemini_tasks:
                logger.warning("⚠️ No tasks found in Gemini response (V2)")
                return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

            # Convert with V2 optimizations
            conversion_result = convert_to_task_items_v2(gemini_tasks, user_difficulty_level)
            task_items = conversion_result["tasks"]
            story_items = conversion_result["stories"]
            optimization_stats = conversion_result["optimization_stats"]

            logger.info(f"✅ V2 Generated {len(task_items)} optimized tasks and {len(story_items)} stories with {optimization_stats['media_excluded_count']} media-excluded tasks")

            return {
                "tasks": task_items,
                "stories": story_items,
                "title": title,
                "usage_metadata": usage_metadata,
                "optimization_stats": optimization_stats,
                "version": "v2"
            }

        except Exception as parse_error:
            logger.error(f"❌ Error parsing/converting response (V2): {str(parse_error)}")
            import traceback
            logger.error(f"Parse error traceback: {traceback.format_exc()}")
            return {"tasks": [], "optimization_stats": {}, "usage_metadata": usage_metadata}

    except Exception as e:
        logger.error(f"❌ Unexpected error in generate function (V2): {str(e)}")
        import traceback
        logger.error(f"Generate error traceback: {traceback.format_exc()}")
        return {"tasks": [], "optimization_stats": {}, "usage_metadata": {}}
