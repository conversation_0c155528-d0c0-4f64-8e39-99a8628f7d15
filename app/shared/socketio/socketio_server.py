"""
Socket.IO Server implementing exact flow specification.

Flow: POST /connect → WebSocket upgrade → stream_starting/ack → binary chunks → completion flows
"""

import socketio
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from app.shared.socketio.connection_manager import ConnectionManager
from app.shared.socketio.status_constants import EventNames
from app.shared.redis import RedisManager
from app.shared.utils.logger import setup_new_logging
# Process audio with prompt_maker
from app.shared.socketio.task_utils import process_audio_with_prompt_maker, save_task_set_and_items, convert_to_socketio_format
# NEW: Queue management imports
from app.shared.redis.queue.audio_queue_manager import AudioQueueManager
from app.shared.socketio.audio_processor import AudioProcessor
from app.shared.config import REDIS_QUEUE_MAX_CONCURRENT_SESSIONS


# Configure logging
logger = setup_new_logging(__name__)


class SocketIOServer:
    """
    Simplified Socket.IO Server implementing exact flow specification with prompt_maker.py integration.

    Flow:
    1. POST /connect → WebSocket upgrade
    2. stream_starting → stream_starting_ack
    3. binary audio chunks (collected in memory buffer)
    4. Completion flows:
       - Normal: stream_completed → stream_completed_ack → task_generation_processing → prompt_maker.py processing → task_generation_complete
       - Forced Stop: stream_stop → stream_stop_ack → task_generation_cancelled
       - Error: stream_error → disconnect and cleanup
    """

    def __init__(self, redis_manager: RedisManager, service_version: str = "v1"):
        """Initialize Socket.IO Server with simplified Redis integration and queue management."""
        self.redis = redis_manager
        self.service_version = service_version  # Track which service version is using this server

        # Create Socket.IO server with Redis adapter for scaling
        self.sio = socketio.AsyncServer(
            cors_allowed_origins="*",
            logger=False,
            engineio_logger=False,
            async_mode='asgi',
            # Redis adapter for multi-instance scaling
            client_manager=socketio.AsyncRedisManager(
                redis_manager.redis_url,
                write_only=False
            )
        )

        # Create ASGI app
        self.app = socketio.ASGIApp(self.sio)

        # Initialize managers
        self.connection_manager = ConnectionManager(redis_manager)

        # NEW: Initialize queue management components
        self.audio_queue_manager = AudioQueueManager(
            redis_manager,
            max_concurrent_sessions=REDIS_QUEUE_MAX_CONCURRENT_SESSIONS
        )
        self.audio_processor = AudioProcessor()

        # Audio collection tracking (replaces real-time processing)
        self.session_audio_buffers: Dict[str, List[bytes]] = {}  # session_id -> list of audio chunks
        self.session_metadata: Dict[str, Dict] = {}  # session_id -> session metadata
        # Note: session_task_sets removed - task sets are only created when tasks are successfully generated

        # Setup event handlers
        self._setup_event_handlers()

    def _setup_event_handlers(self) -> None:
        """Setup Socket.IO event handlers following exact flow specification."""

        @self.sio.event
        async def connect(sid: str, environ: dict, auth: Optional[dict] = None):
            """Handle WebSocket connection after POST /connect."""
            try:
                logger.info(f"WebSocket connecting: {sid}")

                # Authenticate using session_token from POST /connect
                user_info = await self._authenticate_connection(sid, auth)
                if not user_info:
                    logger.warning(f"Authentication failed for {sid}")
                    await self.sio.disconnect(sid)
                    return False

                # Register connection with configuration data and current_user context
                await self.connection_manager.register_connection(
                    sid,
                    user_info["user_id"],
                    user_info.get("session_id"),
                    session_token=user_info.get("session_token"),
                    difficulty=user_info.get("difficulty", "easy"),
                    num_tasks=user_info.get("num_tasks", 3),
                    chunk_threshold=user_info.get("chunk_threshold", 20),
                    current_user=user_info.get("current_user")  # Store current_user context
                )

                logger.info(f"WebSocket connected: {sid} (user: {user_info['user_id']})")
                return True

            except Exception as e:
                logger.error(f"Connection error for {sid}: {e}")
                await self._handle_backend_error(sid, "connection_error", str(e))
                return False

        @self.sio.event
        async def disconnect(sid: str):
            """Handle unexpected client disconnection."""
            try:
                logger.info(f"Client disconnecting: {sid}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if connection_info:
                    session_id = connection_info.get("session_id")
                    if session_id:
                        # Log unexpected disconnect for monitoring
                        logger.warning(f"Unexpected disconnect for session {session_id}")

                        # Clean up session tracking
                        # Note: session_task_sets is no longer used since we don't pre-create task sets
                        if session_id in self.session_audio_buffers:
                            del self.session_audio_buffers[session_id]
                        if session_id in self.session_metadata:
                            del self.session_metadata[session_id]

                        logger.info(f"🧹 Cleaned up session data for disconnected session {session_id}")

                    # Clean up session and connection
                    await self._cleanup_session_and_connection(sid)

            except Exception as e:
                logger.error(f"Error in disconnect handler for {sid}: {e}")

        @self.sio.event
        async def stream_starting(sid: str, data: dict):
            """Handle stream_starting event - Step 1 of flow specification."""
            try:
                logger.info(f"🎙️ Stream starting from {sid} with data: {data}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                # Use frontend session_id if provided, otherwise create one
                logger.info(f"📥 Data type: {type(data)}, keys: {list(data.keys()) if isinstance(data, dict) else 'not dict'}")
                session_id = data.get("session_id") if isinstance(data, dict) else None
                logger.info(f"🔍 Extracted session_id: '{session_id}'")

                if not session_id:
                    session_id = f"{connection_info['user_id']}_{int(datetime.now().timestamp() * 1000)}"
                    logger.info(f"🔧 Created new session_id: {session_id} (frontend did not provide one)")
                else:
                    logger.info(f"✅ Using frontend session_id: {session_id}")

                # Store session info
                await self.connection_manager.update_connection(sid, {"session_id": session_id})

                # Initialize audio buffer for this session
                self.session_audio_buffers[session_id] = []

                # Store session metadata
                self.session_metadata[session_id] = {
                    "difficulty": connection_info.get("difficulty", "easy"),
                    "num_tasks": connection_info.get("num_tasks", 3),
                    "user_id": connection_info["user_id"],
                    "created_at": datetime.now(timezone.utc).isoformat()
                }

                logger.info(f"🎯 Session {session_id} ready - audio buffer initialized")

                # Send stream_starting_ack - backend ready to receive audio chunks
                ack_message = {
                    "session_id": session_id,
                    "message": "Backend ready to receive audio chunks",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_starting_ack', ack_message, room=sid)

                logger.info(f"✅ Backend ready for session {session_id} (real-time processor initialized)")

            except Exception as e:
                logger.error(f"❌ Error in stream_starting: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def binary_data(sid: str, data):
            """Handle binary audio chunks - Step 2 of flow specification."""
            try:
                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                session_id = connection_info.get("session_id")
                if not session_id:
                    await self._handle_backend_error(sid, "session_error", "No active session")
                    return

                # Handle different data types from frontend
                if isinstance(data, dict):
                    # Check if this dict contains audio data (bytes)
                    audio_data = None
                    metadata = {}

                    # Extract audio data and metadata from the dictionary
                    for key, value in data.items():
                        if isinstance(value, bytes):
                            # Found bytes data - this is the audio
                            audio_data = value
                            logger.debug(f"Found audio data in dict key '{key}': {len(value)} bytes")
                        else:
                            # This is metadata - test if it's JSON serializable
                            try:
                                import json
                                json.dumps(value)
                                metadata[key] = value
                            except (TypeError, ValueError):
                                logger.debug(f"Skipping non-JSON-serializable metadata key '{key}': {type(value)}")

                    if audio_data:
                        # Process as audio data with extracted metadata
                        logger.debug(f"Processing audio data from dict: {len(audio_data)} bytes with metadata: {list(metadata.keys())}")
                        # Store metadata if any
                        if metadata:
                            await self.connection_manager.update_connection(sid, {
                                "pending_chunk_metadata": metadata
                            })
                        # Continue to audio processing below with extracted audio data
                        data = audio_data
                    else:
                        # Pure metadata without audio data
                        if metadata:
                            await self.connection_manager.update_connection(sid, {
                                "pending_chunk_metadata": metadata
                            })
                            logger.debug(f"Received chunk metadata: {metadata.get('chunk_id', 'unknown')}")
                        return

                # Handle binary audio data (both from dict extraction and direct bytes)
                if isinstance(data, (bytes, str)):
                    logger.info(f"🎵 Processing binary audio data: {len(data)} bytes")

                    if isinstance(data, str):
                        # Convert base64 or hex string to bytes if needed
                        try:
                            import base64
                            data = base64.b64decode(data)
                        except Exception:
                            logger.warning("Could not decode string data, treating as raw bytes")
                            data = data.encode() if isinstance(data, str) else data

                    # Re-fetch connection info to ensure it's current
                    connection_info = await self.connection_manager.get_connection(sid)
                    if not connection_info:
                        logger.error(f"❌ Connection info lost for {sid} during binary data processing")
                        await self._handle_backend_error(sid, "connection_error", "Connection lost during processing")
                        return

                    # Get pending metadata if available
                    pending_metadata = connection_info.get("pending_chunk_metadata") or {}

                    # Use session_id from frontend metadata if available, otherwise use connection session_id
                    chunk_session_id = pending_metadata.get("session_id", session_id)

                    # Log session ID usage for debugging
                    if chunk_session_id != session_id:
                        logger.info(f"📦 Using frontend session_id {chunk_session_id} instead of connection session_id {session_id}")

                    logger.info(f"📦 Collecting audio chunk for session_id: {chunk_session_id} (connection: {session_id}, chunk_size: {len(data)} bytes)")

                    # Add audio chunk to buffer for this session
                    if chunk_session_id in self.session_audio_buffers:
                        self.session_audio_buffers[chunk_session_id].append(data)
                        chunk_count = len(self.session_audio_buffers[chunk_session_id])
                        logger.debug(f"� Added chunk {chunk_count} to buffer for session {chunk_session_id}")
                    else:
                        # Initialize buffer if not exists (shouldn't happen if stream_starting was called)
                        self.session_audio_buffers[chunk_session_id] = [data]
                        logger.warning(f"⚠️ Initialized missing audio buffer for session {chunk_session_id}")

                    # Task set will be created only when tasks are successfully generated
                    # No need to create empty task sets during audio streaming

                    # Clear pending metadata
                    await self.connection_manager.update_connection(sid, {
                        "pending_chunk_metadata": None
                    })

                else:
                    logger.warning(f"Unexpected data type in binary_data: {type(data)}")

            except Exception as e:
                logger.error(f"❌ Error processing audio chunk: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def stream_completed(sid: str, data: dict):
            """Handle stream_completed event - Step 3A of flow specification (Normal Flow)."""
            try:
                logger.info(f"🏁 Stream completed from {sid} with data: {data}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                # Get session_id from connection
                connection_session_id = connection_info.get("session_id")
                if not connection_session_id:
                    await self._handle_backend_error(sid, "session_error", "No session ID found in connection")
                    return

                # Get session_id from frontend data (for validation)
                frontend_session_id = data.get("session_id")
                logger.info(f"🔍 Session ID comparison: connection={connection_session_id}, frontend={frontend_session_id}")

                # Use frontend session_id if provided and different from connection
                if frontend_session_id:
                    if frontend_session_id != connection_session_id:
                        logger.warning(f"⚠️ Session ID mismatch: connection={connection_session_id}, frontend={frontend_session_id}")
                        logger.info(f"✅ Using frontend session_id: {frontend_session_id}")
                        session_id = frontend_session_id
                    else:
                        logger.info(f"✅ Session ID consistent: {frontend_session_id}")
                        session_id = frontend_session_id
                else:
                    logger.info(f"⚠️ No session_id in stream_completed data, using connection session_id: {connection_session_id}")
                    session_id = connection_session_id

                logger.info(f"🎯 Final session_id for task generation: {session_id}")

                # Send stream_completed_ack
                ack_message = {
                    "session_id": session_id,
                    "message": "Stream completion acknowledged",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_completed_ack', ack_message, room=sid)

                # Send task_generation_processing
                processing_message = {
                    "session_id": session_id,
                    "message": "Processing audio for task generation",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('task_generation_processing', processing_message, room=sid)

                # Get audio chunks and metadata BEFORE queue processing
                # First, try to find audio chunks under the resolved session_id
                audio_chunks = self.session_audio_buffers.get(session_id, [])
                session_metadata = self.session_metadata.get(session_id, {})

                # If no chunks found, try to find them under connection session_id (fallback)
                if not audio_chunks and session_id != connection_session_id:
                    logger.warning(f"⚠️ No chunks found for session_id {session_id}, trying connection session_id {connection_session_id}")
                    audio_chunks = self.session_audio_buffers.get(connection_session_id, [])
                    session_metadata = self.session_metadata.get(connection_session_id, {})
                    if audio_chunks:
                        logger.info(f"✅ Found {len(audio_chunks)} chunks under connection session_id {connection_session_id}")
                        # Update session_id to the one that actually has the chunks
                        session_id = connection_session_id

                # NEW: Check queue status and decide processing path
                # This happens AFTER existing acks are sent to maintain compatibility
                tasks, task_set_id, should_continue_existing_flow = await self._process_or_queue_task_generation(
                    sid, session_id, audio_chunks, session_metadata, connection_info
                )

                # If queued, don't continue with existing processing flow
                if not should_continue_existing_flow:
                    logger.info(f"Session {session_id} queued - skipping immediate processing")
                    # Don't disconnect yet - let queue worker handle completion
                    return

                # Process collected audio with new prompt_maker approach (EXISTING FLOW PRESERVED)
                # Audio chunks and metadata already retrieved above

                # Debug: Log all available session IDs in buffers
                if not audio_chunks:
                    available_sessions = list(self.session_audio_buffers.keys())
                    logger.warning(f"⚠️ No audio chunks found. Available sessions in buffer: {available_sessions}")
                    logger.warning(f"⚠️ Looked for session_id: {session_id}, connection_session_id: {connection_session_id}")

                if audio_chunks:
                    logger.info(f"🎯 Processing {len(audio_chunks)} audio chunks with prompt_maker for session {session_id}")

                    # Combine all audio chunks into single bytes object
                    combined_audio = b''.join(audio_chunks)
                    logger.info(f"📦 Combined audio size: {len(combined_audio)} bytes")

                    # Get session parameters
                    num_tasks = session_metadata.get("num_tasks", 4)
                    current_user = await self._get_current_user_from_connection(connection_info)

                    # Store audio in MinIO first
                    audio_storage_info = None
                    if current_user:
                        try:
                            _, processing_metadata = self.audio_processor.process_session_audio(
                                audio_chunks=audio_chunks,
                                current_user=current_user,
                                session_id=session_id,
                                minio_client=current_user.minio
                            )
                            audio_storage_info = processing_metadata["storage_info"]
                            logger.info(f"📁 Audio stored in MinIO: {audio_storage_info.get('object_path') if audio_storage_info else 'None'}")
                        except Exception as e:
                            logger.error(f"❌ Error storing audio in MinIO: {e}")
                            # Continue without audio storage info

                    tasks_data = await process_audio_with_prompt_maker(
                        current_user,
                        combined_audio,
                        num_tasks=num_tasks,
                    )

                    # Get current user and save tasks to database
                    # No pre-existing task_set_id since we only create when tasks are generated
                    task_set_id = None

                    if current_user and tasks_data.get("tasks"):
                        # Save tasks with priority processing (first task story image generated before response)
                        save_result = await save_task_set_and_items(
                            current_user, session_id, tasks_data, task_set_id, audio_storage_info, self.sio
                        )

                        # Check if saving was successful
                        if save_result.get("status") == "error":
                            logger.error(f"❌ Failed to save tasks for session {session_id}: {save_result.get('error')}")
                            task_set_id = None
                            error_message = save_result.get("error", "Failed to save tasks")
                        else:
                            task_set_id = save_result["task_set_id"]
                            logger.info(f"✅ Generated and saved {len(tasks_data.get('tasks', []))} tasks for session {session_id}")

                            # Start image generation after response (non-blocking)
                            asyncio.create_task(self._process_image_generation_after_response(
                                current_user, session_id, tasks_data, task_set_id
                            ))
                    else:
                        logger.warning(f"⚠️ No tasks generated or no current_user for session {session_id}")
                        task_set_id = None
                        error_message = "No tasks generated from the provided input"
                else:
                    logger.warning(f"⚠️ No audio chunks found for session {session_id}")
                    tasks = []
                    task_set_id = None
                    error_message = "No audio chunks found for processing"

                # Clean up session tracking
                # Note: session_task_sets is no longer used since we don't pre-create task sets
                # Clean up both the resolved session_id and connection_session_id (in case they're different)
                sessions_to_clean = {session_id, connection_session_id}
                for sid_to_clean in sessions_to_clean:
                    if sid_to_clean in self.session_audio_buffers:
                        del self.session_audio_buffers[sid_to_clean]
                        logger.info(f"🧹 Cleaned up audio buffer for session {sid_to_clean}")
                    if sid_to_clean in self.session_metadata:
                        del self.session_metadata[sid_to_clean]
                        logger.info(f"🧹 Cleaned up metadata for session {sid_to_clean}")

                # Don't create fallback task_set_id when no tasks are generated
                # task_set_id will be None if no tasks were generated

                # Determine status and message based on task generation success
                if task_set_id:
                    status = "completed"
                    message = "Task generation completed successfully"
                    event_name = EventNames.ToFrontend.TASK_GENERATION_COMPLETE

                    # Send task_generation_complete for successful generation (only task_set_id)
                    complete_message = {
                        "session_id": session_id,
                        "message": message,
                        "timestamp": datetime.now().isoformat(),
                        "task_set_id": task_set_id,
                        "status": status
                    }
                else:
                    status = "failed"
                    message = error_message if 'error_message' in locals() else "Task generation failed: No tasks generated"
                    event_name = EventNames.ToFrontend.TASK_GENERATION_FAILED
                    logger.error(f"Task generation failed for session {session_id}: {message}")

                    # Send task_generation_failed for failed generation
                    complete_message = {
                        "session_id": session_id,
                        "message": message,
                        "timestamp": datetime.now().isoformat(),
                        "status": status,
                        "error": message
                    }
                    # No task_set_id field when generation failed

                await self.sio.emit(event_name, complete_message, room=sid)

                # Log the complete message for debugging
                task_set_log = task_set_id if task_set_id else "None (no tasks generated)"
                logger.info(f"📤 Sent {event_name} to frontend: task_set_id={task_set_log}, status={status}")

                # In-memory storage will be cleaned up automatically during disconnect
                # No need for explicit cleanup since we're not using Redis session storage
                logger.info(f"📝 Task generation completed for session {session_id} (status: {status})")

                # Disconnect following flow specification
                await self._cleanup_and_disconnect(sid, "Stream completed")

                logger.info(f"✅ Stream completed for session {session_id}")

            except Exception as e:
                logger.error(f"❌ Error in stream_completed: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

        @self.sio.event
        async def stream_stop(sid: str, data: dict):
            """Handle stream_stop event - Step 3B of flow specification (Forced Stop Flow)."""
            try:
                logger.info(f"🛑 Stream stop from {sid}")

                # Get connection info
                connection_info = await self.connection_manager.get_connection(sid)
                if not connection_info:
                    await self._handle_backend_error(sid, "connection_error", "Connection not found")
                    return

                session_id = connection_info.get("session_id")
                if not session_id:
                    await self._handle_backend_error(sid, "session_error", "No session ID found")
                    return

                # Clean up session tracking
                # Note: session_task_sets is no longer used since we don't pre-create task sets
                if session_id in self.session_audio_buffers:
                    del self.session_audio_buffers[session_id]
                if session_id in self.session_metadata:
                    del self.session_metadata[session_id]

                logger.info(f"🧹 Cleaned up session data for stopped session {session_id}")

                # Send stream_stop_ack
                ack_message = {
                    "session_id": session_id,
                    "message": "Stream stop acknowledged",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('stream_stop_ack', ack_message, room=sid)

                # Optionally send task_generation_cancelled
                cancelled_message = {
                    "session_id": session_id,
                    "message": "Task generation cancelled",
                    "timestamp": datetime.now().isoformat()
                }
                await self.sio.emit('task_generation_cancelled', cancelled_message, room=sid)

                # Disconnect following flow specification
                await self._cleanup_and_disconnect(sid, "Stream stopped")

                logger.info(f"🛑 Stream stopped for session {session_id}")

            except Exception as e:
                logger.error(f"❌ Error in stream_stop: {e}")
                await self._handle_backend_error(sid, "backend_error", str(e))

    async def _authenticate_connection(self, sid: str, auth: Optional[dict]) -> Optional[Dict[str, Any]]:
        """Authenticate WebSocket connection using session token and retrieve pre-stored user context."""
        try:
            if not auth or not auth.get("session_token"):
                return None

            # Validate session token and get session data with pre-stored user context
            # Use dynamic import based on service version
            if self.service_version == "v2":
                from app.v2.api.socket_service_v2.routes.socket_auth import get_session_by_token
            else:
                from app.v1.api.socket_service.routes.socket_auth import get_session_by_token

            session_data = get_session_by_token(auth["session_token"])

            if not session_data:
                return None

            # Retrieve pre-stored current_user context from HTTP POST authentication phase
            current_user = session_data.get("current_user")
            if not current_user:
                logger.error(f"No current_user context found in session data for {sid}")
                return None

            # Extract user and session information
            user_id = session_data["user_id"]
            session_id = session_data["session_id"]
            config = session_data.get("configuration", {})

            logger.info(f"Retrieved pre-stored user context for {user_id} (session: {session_id})")

            return {
                "user_id": user_id,
                "session_id": session_id,
                "session_token": auth["session_token"],
                "authenticated": True,
                "difficulty": config.get("difficulty", "easy"),
                "num_tasks": config.get("num_tasks", 3),
                "chunk_threshold": config.get("chunk_threshold", 20),
                "current_user": current_user  # Use pre-stored current_user context
            }

        except Exception as e:
            logger.error(f"Authentication error for {sid}: {e}")
            import traceback
            logger.error(f"Authentication traceback: {traceback.format_exc()}")
            return None

    async def _get_current_user_from_connection(self, connection_info: Dict[str, Any]) -> Optional[Any]:
        """
        Get current_user from session token since UserTenantDB objects can't be stored in Redis.

        The connection manager filters out non-JSON-serializable objects like UserTenantDB,
        so we need to retrieve the user context from the session data instead.
        """
        try:
            if not connection_info:
                logger.error("❌ No connection_info provided")
                return None

            # Get session token to retrieve fresh user context from session storage
            session_token = connection_info.get("session_token")
            if not session_token:
                logger.error("❌ No session_token in connection_info")
                return None

            logger.info(f"🔍 Retrieving current_user for session_token: {session_token}")

            # Get session data which contains the current_user
            # Use dynamic import based on service version
            if self.service_version == "v2":
                from app.v2.api.socket_service_v2.routes.socket_auth import get_session_by_token
            else:
                from app.v1.api.socket_service.routes.socket_auth import get_session_by_token

            session_data = get_session_by_token(session_token)

            if not session_data:
                logger.error(f"❌ No session data found for token: {session_token}")
                return None

            current_user = session_data.get("current_user")
            if not current_user:
                logger.error(f"❌ No current_user in session data for token: {session_token}")
                return None

            logger.info("✅ Successfully retrieved current_user from session data")
            return current_user

        except Exception as e:
            logger.error(f"❌ Error getting current_user from connection: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    async def _handle_backend_error(self, sid: str, reason: str, error_details: str):
        """Handle backend errors following flow specification."""
        try:
            # Get connection info
            connection_info = await self.connection_manager.get_connection(sid)
            if connection_info:
                session_id = connection_info.get("session_id")
                if session_id:
                    # Send stream_error message
                    error_message = {
                        "session_id": session_id,
                        "reason": reason,
                        "message": f"Stream error: {reason}",
                        "timestamp": datetime.now().isoformat()
                    }
                    if error_details:
                        error_message["error_details"] = error_details
                    await self.sio.emit('stream_error', error_message, room=sid)

            # Disconnect and remove from all sessions
            await self._cleanup_and_disconnect(sid, f"Backend error: {reason}")

        except Exception as e:
            logger.error(f"Error handling backend error for {sid}: {e}")
            # Force disconnect if error handling fails
            try:
                await self.sio.disconnect(sid)
            except Exception:
                pass

    # _create_task_set method removed - task sets are now only created when tasks are successfully generated

    # NEW: Queue management helper methods
    async def _handle_audio_queue_logic(self, sid: str, session_id: str, audio_chunks: List[bytes],
                                       connection_info: Dict[str, Any]) -> None:
        """
        NEW: Handle MinIO storage and queue decisions without breaking existing flow.

        This method is called AFTER existing stream_completed_ack is sent.
        """
        try:
            # Get current_user context
            current_user = await self._get_current_user_from_connection(connection_info)
            if not current_user:
                logger.error(f"No current_user context for queue processing: {sid}")
                return

            # Process and store audio in MinIO
            input_content, processing_metadata = await self.audio_processor.process_session_audio(
                audio_chunks=audio_chunks,
                current_user=current_user,
                session_id=session_id,
                minio_client=current_user.minio
            )

            user_id = processing_metadata["user_id"]
            tenant_id = processing_metadata["tenant_id"]

            logger.info(f"Audio stored in MinIO for user {user_id}, session {session_id}")

            # Check queue capacity and user eligibility
            can_process_immediately = await self.audio_queue_manager.can_process_immediately()
            can_user_queue = await self.audio_queue_manager.can_user_queue(user_id)

            if can_process_immediately and can_user_queue:
                # Process immediately (existing flow continues)
                logger.info(f"Processing immediately for user {user_id}, session {session_id}")
                # No additional action needed - existing flow will handle processing

            elif can_user_queue:
                # Add to queue
                taskset_id = await self.audio_queue_manager.create_taskset(
                    user_id=user_id,
                    tenant_id=tenant_id,
                    session_id=session_id,
                    input_content=input_content.dict(),
                    socket_sid=sid
                )

                queue_position = await self.audio_queue_manager.get_queue_position(taskset_id)

                # Send NEW queue status event
                await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_QUEUED, {
                    "session_id": session_id,
                    "taskset_id": taskset_id,
                    "message": "Your session has been added to the processing queue",
                    "queue_position": queue_position,
                    "estimated_wait_seconds": queue_position * 30,
                    "timestamp": datetime.now().isoformat()
                }, room=sid)

                logger.info(f"Queued TaskSet {taskset_id} for user {user_id} at position {queue_position}")

            else:
                # User has existing session - add to pending
                session_data = {
                    "session_id": session_id,
                    "input_content": input_content.dict(),
                    "socket_sid": sid,
                    "tenant_id": tenant_id,
                    "created_at": datetime.now().isoformat()
                }

                await self.audio_queue_manager.add_to_user_pending(user_id, session_data)
                estimated_wait = await self.audio_queue_manager.get_estimated_wait(user_id)

                # Send NEW pending status event
                await self.sio.emit(EventNames.ToFrontend.TASK_GENERATION_PENDING, {
                    "session_id": session_id,
                    "message": "Your previous session is still processing. This session will be processed next.",
                    "estimated_wait_seconds": estimated_wait,
                    "timestamp": datetime.now().isoformat()
                }, room=sid)

                logger.info(f"Added pending session for user {user_id} with estimated wait {estimated_wait}s")

        except Exception as e:
            logger.error(f"Error in queue logic for {sid}: {e}")
            # Don't break existing flow - just log the error

    async def _process_or_queue_task_generation(self, sid: str, session_id: str, audio_chunks: List[bytes],
                                              session_metadata: Dict[str, Any], connection_info: Dict[str, Any]) -> tuple:
        """
        NEW: Enhanced task generation with queue management.

        Returns:
            Tuple of (tasks, task_set_id, should_continue_existing_flow)
        """
        try:
            # Get current_user context
            current_user = await self._get_current_user_from_connection(connection_info)
            if not current_user:
                logger.error(f"No current_user context for task generation: {sid}")
                return [], None, True  # Continue with existing flow

            user_id = str(current_user.user.id)

            # Check if we can process immediately
            can_process_immediately = await self.audio_queue_manager.can_process_immediately()
            can_user_queue = await self.audio_queue_manager.can_user_queue(user_id)

            if can_process_immediately and can_user_queue:
                # Process immediately with existing logic
                logger.info(f"Processing immediately for user {user_id}")
                return [], None, True  # Let existing flow handle it

            else:
                # Queue the session - existing flow should NOT continue
                await self._handle_audio_queue_logic(sid, session_id, audio_chunks, connection_info)
                return [], None, False  # Don't continue existing flow

        except Exception as e:
            logger.error(f"Error in process_or_queue_task_generation: {e}")
            return [], None, True  # Continue with existing flow on error

    async def _process_image_generation_after_response(
            self, current_user: Any, session_id: str, tasks_data: Dict[str, Any], task_set_id: str
    ):
        """
        Process image generation after response has been sent to frontend.

        Args:
            current_user: Current user context
            session_id: Session identifier
            tasks_data: Tasks data containing image identification tasks
            task_set_id: Task set ID for updating database
        """
        logger.info(f"🔄 Starting _process_image_generation_after_response for task_set_id: {task_set_id}")
        
        if not current_user:
            logger.warning("⚠️  Skipping image generation - current_user is missing")
            return
            
        if not task_set_id:
            logger.warning("⚠️  Skipping image generation - task_set_id is missing")
            return

        try:
            tasks = tasks_data.get("tasks", [])
            logger.info(f"🔍 Processing {len(tasks)} tasks for image generation")
            
            if not tasks:
                logger.warning("⚠️  No tasks found in tasks_data for image generation")
                return

            # Get task item IDs for the task set
            task_items = await current_user.async_db.task_items.find(
                {"task_set_id": ObjectId(task_set_id)},
                {"_id": 1, "type": 1, "question": 1, "title": 1}
            ).to_list(length=len(tasks))
            
            logger.info(f"📋 Retrieved {len(task_items)} task items from database")

            # Map task indices to their corresponding item IDs
            item_ids = [str(item["_id"]) for item in task_items]
            logger.info(f"📌 Mapped {len(item_ids)} task item IDs")


            # Find image identification tasks and their keywords
            image_tasks = []
            for i, task in enumerate(tasks):
                task_type = task.get("type", "single_choice")
                is_image_task = (
                    task_type == "image_identification" or 
                    task_type == "image_identify" or
                    task_type == QuizType.IMAGE_IDENTIFICATION.value
                )
                
                logger.info(f"🔍 Task {i} - Type: '{task_type}', is_image_task: {is_image_task}")
                logger.info(f"   Task data: {json.dumps(task, ensure_ascii=False, default=str)[:200]}...")
                
                if is_image_task:
                    # Try to extract keyword from answer_hint or answer
                    question_data = task.get("question", {})
                    keyword = question_data.get("answer_hint", "") or question_data.get("answer", "")
                    
                    if keyword:
                        logger.info(f"🖼️  Found image task with keyword: {keyword}")
                        image_tasks.append((i, keyword, task_type))
                    else:
                        logger.warning(f"⚠️  Image task {i} has no valid keyword (answer_hint or answer)")
                else:
                    logger.debug(f"Skipping non-image task of type: {task_type}")
            
            if image_tasks:
                logger.info(f"🚀 Found {len(image_tasks)} image identification tasks to process")
                # Start image generation in background
                try:
                    asyncio.create_task(
                        _generate_images_realtime(current_user, task_set_id, image_tasks, item_ids)
                    )
                    logger.info("✅ Successfully started image generation task")
                except Exception as e:
                    logger.error(f"❌ Failed to start image generation task: {str(e)}", exc_info=True)
            else:
                logger.info(f"ℹ️ No image tasks found for session {session_id}")

        except Exception as e:
            logger.error(f"❌ Error in post-response image generation for session {session_id}: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise  # Re-raise to see full trace in logs





    async def _cleanup_session_and_connection(self, sid: str):
        """Clean up session and connection."""
        try:
            connection_info = await self.connection_manager.get_connection(sid)
            if connection_info:
                session_id = connection_info.get("session_id")
                if session_id:
                    # Clean up session tracking
                    # Note: session_task_sets is no longer used since we don't pre-create task sets
                    if session_id in self.session_audio_buffers:
                        del self.session_audio_buffers[session_id]
                    if session_id in self.session_metadata:
                        del self.session_metadata[session_id]

                    logger.info(f"🧹 Cleaned up session data for {session_id}")

                await self.connection_manager.unregister_connection(sid)
        except Exception as e:
            logger.error(f"Error cleaning up session and connection for {sid}: {e}")

    async def _cleanup_and_disconnect(self, sid: str, reason: str):
        """Cleanup session and disconnect client."""
        try:
            logger.info(f"Cleaning up and disconnecting {sid}: {reason}")
            await self._cleanup_session_and_connection(sid)
            await self.sio.disconnect(sid)
            logger.info(f"✅ Successfully cleaned up and disconnected {sid}")
        except Exception as e:
            logger.error(f"Error during cleanup and disconnect for {sid}: {e}")

    async def setup(self) -> None:
        """Setup Socket.IO server."""
        try:
            logger.info("Setting up simplified Socket.IO server...")
            await self.redis.ensure_connected()
            await self.connection_manager.setup()
            logger.info("Socket.IO server setup completed")
        except Exception as e:
            logger.error(f"Failed to setup Socket.IO server: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup Socket.IO server."""
        try:
            logger.info("Cleaning up Socket.IO server...")

            # Clean up all session tracking
            # Note: session_task_sets is no longer used since we don't pre-create task sets
            self.session_audio_buffers.clear()
            self.session_metadata.clear()

            await self.connection_manager.cleanup()
            logger.info("Socket.IO server cleanup completed")
        except Exception as e:
            logger.error(f"Error during Socket.IO cleanup: {e}")
