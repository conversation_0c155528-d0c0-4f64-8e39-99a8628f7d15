from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional, List, Dict, Any
from bson import ObjectId
from datetime import timedelta
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

router = APIRouter()


@router.get("/set/{task_set_id}/stories")
async def get_story_ids_for_task_set(
    task_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get list of story IDs for a specific task set ID.

    Args:
        task_set_id: The task set ID
        current_user: Current user information

    Returns:
        List of story IDs for the specified task set
    """
    try:
        loggers.info(f"Getting story IDs for task set {task_set_id} for user {current_user.user.username}")

        # Validate task_set_id
        if not task_set_id or not ObjectId.is_valid(task_set_id):
            raise HTTPException(status_code=400, detail="Invalid task set ID")

        # Get all story IDs for this task set (only return _id field)
        story_ids = []

        # Query stories by task_set_id, collection_id, or v2_collection_id
        query = {"$or": [
            {"task_set_id": task_set_id},
            {"collection_id": task_set_id},
            {"v2_collection_id": task_set_id}
        ]}

        cursor = current_user.async_db.story_steps.find(query, {"_id": 1})
        stories = await cursor.to_list(length=None)

        # Extract story IDs
        story_ids = [str(story["_id"]) for story in stories]

        # Return the story IDs
        return {
            "story_ids": story_ids,
            "count": len(story_ids),
            "task_set_id": task_set_id
        }
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting story IDs for task set: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting story IDs for task set: {str(e)}")






@router.get("/{story_id}")
async def get_story_by_id(
    story_id: str,
    fields: Optional[List[str]] = Query(
        default=["steps", "total_steps", "completed_steps", "status", "created_at", "updated_at","script","metadata"],
        description="Fields to retrieve from story"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a single story object by ID.

    Args:
        story_id: The story ID
        fields: Fields to retrieve from story
        current_user: Current user information

    Returns:
        The complete story object with selected fields
    """
    try:
        loggers.info(f"Getting story {story_id} for user {current_user.user.username}")

        # Validate story_id
        if not story_id or not ObjectId.is_valid(story_id):
            raise HTTPException(status_code=400, detail="Invalid story ID")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get the story
        story = await current_user.async_db.story_steps.find_one(
            {"_id": ObjectId(story_id)}, projection
        )

        if not story:
            raise HTTPException(status_code=404, detail=f"Story {story_id} not found")

        # Convert ObjectId to string
        story["id"] = str(story.pop("_id"))

        # Generate presigned URL if story has metadata with object_name
        if "metadata" in story and "object_name" in story["metadata"]:
            try:
                presigned_url = current_user.minio.get_presigned_url(
                    bucket_name=current_user.minio_bucket_name,
                    object_name=story["metadata"]["object_name"],
                    expires=timedelta(hours=24),
                    method="GET"
                )
                story["metadata"]["url"] = presigned_url
            except Exception as e:
                loggers.error(f"Error generating URL for {story['metadata']['object_name']}: {e}")
                story["metadata"]["url"] = None

        return story
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting story: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting story: {str(e)}")


@router.get("/story/{story_id}")
async def fetch_story_by_id(
    story_id: str,
    fields: Optional[List[str]] = Query(
        default=["steps", "total_steps", "completed_steps", "status", "created_at", "updated_at", "collection_id"],
        description="Fields to retrieve from story"
    ),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Fetch a single story by ID (alternative endpoint).

    Args:
        story_id: The story ID
        fields: Fields to retrieve from story
        current_user: Current user information

    Returns:
        The complete story object with selected fields
    """
    try:
        loggers.info(f"Fetching story {story_id} for user {current_user.user.username}")

        # Validate story_id
        if not story_id or not ObjectId.is_valid(story_id):
            raise HTTPException(status_code=400, detail="Invalid story ID")

        # Create projection for MongoDB query
        projection = {field: 1 for field in fields}
        projection["_id"] = 1  # Always include _id

        # Get the story
        story = await current_user.async_db.story_steps.find_one(
            {"_id": ObjectId(story_id)}, projection
        )

        if not story:
            raise HTTPException(status_code=404, detail=f"Story {story_id} not found")

        # Convert ObjectId to string
        story["id"] = str(story.pop("_id"))

        # Generate presigned URL if story has metadata with object_name
        if "metadata" in story and "object_name" in story["metadata"]:
            try:
                presigned_url = current_user.minio.get_presigned_url(
                    bucket_name=current_user.minio_bucket_name,
                    object_name=story["metadata"]["object_name"],
                    expires=timedelta(hours=24),
                    method="GET"
                )
                story["metadata"]["url"] = presigned_url
            except Exception as e:
                loggers.error(f"Error generating URL for {story['metadata']['object_name']}: {e}")
                story["metadata"]["url"] = None

        return story
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error fetching story: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching story: {str(e)}")
