"""
User authentication and management routes.
"""

from fastapi import APIRouter, HTTPException, Depends, Response
from fastapi.responses import RedirectResponse
from bson import ObjectId
from datetime import datetime, timedelta
import secrets
import json
import httpx
import base64
import asyncio
from urllib.parse import urlencode, parse_qs, urlparse

from app.shared.utils.logger import setup_new_logging
from app.shared.security import (
    get_tenant_info, require_roles, create_access_token,
    verify_password, create_invitation_token, verify_invitation_token, ph,
    verify_google_token
)
from app.shared.database import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from app.shared.models.security import (
    GenericOAuth2Form, OAuth2PasswordRequestFormWithClientID, ChangePasswordRequest, ResetPasswordRequest,
    GoogleAuthRequest, SignupRequest, LoginRequest, AuthResponse
)
from app.shared.models.user import AgentInvitation, AgentRegistration, UserTenantDB, OnboardingRequest, OnboardingResponse, UserProfile

from app.shared.utils.mongo_helper import convert_objectid_to_str, serialize_mongo_doc
import os
from dotenv import load_dotenv

load_dotenv()
# Configure logging
loggers = setup_new_logging(__name__)

# Google OAuth configuration
GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")  # Replace with your actual client ID from environment variables
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")  # Replace with your actual client secret from environment variables
GOOGLE_DISCOVERY_URL = "https://accounts.google.com/.well-known/openid-configuration"
GOOGLE_AUTH_URL = "https://accounts.google.com/o/oauth2/auth"
GOOGLE_TOKEN_URL = "https://oauth2.googleapis.com/token"
GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v3/userinfo"

# Create router
router = APIRouter()

def create_auth_response(user: dict, access_token: str, tenant_id: str, tenant_name: str, tenant_slug: str) -> AuthResponse:
    """
    Create a standardized authentication response.

    Args:
        user: User document from database
        access_token: JWT access token
        tenant_id: Tenant ID
        tenant_name: Tenant name/label
        tenant_slug: Tenant slug/client ID

    Returns:
        Standardized AuthResponse object
    """
    # Convert ObjectId to string if needed
    user_data = convert_objectid_to_str(user)

    # Format current login as ISO string if it exists
    last_login = None
    if user_data.get("last_login"):
        if isinstance(user_data["last_login"], datetime):
            last_login = user_data["last_login"].isoformat()
        else:
            last_login = str(user_data["last_login"])

    # Format previous login as ISO string if it exists
    previous_login = None
    if user_data.get("previous_login"):
        if isinstance(user_data["previous_login"], datetime):
            previous_login = user_data["previous_login"].isoformat()
        else:
            previous_login = str(user_data["previous_login"])

    # Create standardized response
    return AuthResponse(
        id=user_data["_id"],
        access_token=access_token,
        token_type="bearer",
        username=user_data["username"],
        email=user_data.get("email"),
        role=user_data["role"],
        tenant_id=tenant_id,
        tenant_label=tenant_name,
        tenant_slug=tenant_slug,
        full_name=user_data.get("full_name"),
        profile_picture=user_data.get("profile_picture"),
        auth_provider=user_data.get("auth_provider", "password"),
        last_login=last_login,
        previous_login=previous_login,
        phone_number=user_data.get("phone_number"),
        country_code=user_data.get("country_code"),
        onboarding_completed=user_data.get("onboarding_completed", False)
    )

@router.post("/signup", response_model=AuthResponse)
async def signup(form_data: GenericOAuth2Form = Depends()):
    """
    User signup endpoint for email/password authentication.
    """
    # Extract data from form_data
    username = form_data.username
    email = form_data.email
    password = form_data.password
    full_name = form_data.full_name
    phone_number = form_data.phone_number
    country_code = form_data.country_code
    client_id = form_data.client_id

    # Validate required fields
    if not username or not email or not password or not client_id:
        raise HTTPException(status_code=400, detail="Missing required fields: username, email, password, client_id")

    # Validate phone number and country code together
    if (phone_number and not country_code) or (country_code and not phone_number):
        raise HTTPException(status_code=400, detail="Phone number and country code must be provided together")

    try:
        # Get tenant information
        tenant_result = await get_tenant_id_and_name_from_slug(client_id)
        tenant_id = str(tenant_result["_id"])
        _, async_tenant_db = await get_db_from_tenant_id(tenant_id)

        # Build single query to check for existing username, email, or phone+country combination
        query_conditions = [
            {"username": username},
            {"email": email}
        ]

        # Add phone+country check if both provided
        if phone_number and country_code:
            query_conditions.append({
                "phone_number": phone_number,
                "country_code": country_code
            })

        # Single check for all conditions
        existing_user = await async_tenant_db.users.find_one({"$or": query_conditions})

        if existing_user:
            # Determine what matched and provide specific error
            if existing_user.get("username") == username:
                raise HTTPException(status_code=400, detail="Username already exists")
            elif existing_user.get("email") == email:
                # Special case: Google-only account can be upgraded with password
                if existing_user.get("auth_provider") == "google" and not existing_user.get("hashed_password"):
                    # Upgrade Google account with password
                    hashed_password = ph.hash(password)
                    update_data = {
                        "hashed_password": hashed_password,
                        "auth_provider": "both",
                        "previous_login": existing_user.get("last_login"),
                        "last_login": datetime.now(),
                        "onboarding_completed": False
                    }

                    if phone_number and country_code:
                        update_data["phone_number"] = phone_number
                        update_data["country_code"] = country_code

                    if full_name and not existing_user.get("full_name"):
                        update_data["full_name"] = full_name

                    await async_tenant_db.users.update_one(
                        {"_id": existing_user["_id"]},
                        {"$set": update_data}
                    )

                    # Update user object
                    existing_user.update(update_data)

                    # Create JWT token
                    access_token = create_access_token(
                        data={"sub": existing_user["username"], "role": existing_user["role"], "tenant_id": tenant_id},
                        expires_delta=timedelta(hours=2)
                    )

                    return create_auth_response(
                        user=existing_user,
                        access_token=access_token,
                        tenant_id=tenant_id,
                        tenant_name=tenant_result["name"],
                        tenant_slug=client_id
                    )
                else:
                    raise HTTPException(status_code=400, detail="Email already exists")
            elif (existing_user.get("phone_number") == phone_number and
                  existing_user.get("country_code") == country_code):
                raise HTTPException(status_code=400, detail="Phone number with this country code already exists")

        # Get or create default role
        default_role = await async_tenant_db.roles.find_one({"name": "agent"})
        if not default_role:
            default_role = {"name": "agent"}
            role_result = await async_tenant_db.roles.insert_one(default_role)
            default_role["_id"] = role_result.inserted_id

        # Create new user
        new_user = {
            "username": username,
            "email": email,
            "hashed_password": ph.hash(password),
            "role": default_role["name"],
            "full_name": full_name,
            "auth_provider": "password",
            "created_at": datetime.now(),
            "last_login": datetime.now(),
            "onboarding_completed": False
        }

        # Add phone fields if provided
        if phone_number and country_code:
            new_user["phone_number"] = phone_number
            new_user["country_code"] = country_code

        # Insert user
        insert_result = await async_tenant_db.users.insert_one(new_user)
        new_user["_id"] = insert_result.inserted_id

        # Create JWT token
        access_token = create_access_token(
            data={"sub": new_user["username"], "role": new_user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(hours=2)
        )

        loggers.info(f"User {username} registered successfully")

        return create_auth_response(
            user=new_user,
            access_token=access_token,
            tenant_id=tenant_id,
            tenant_name=tenant_result["name"],
            tenant_slug=client_id
        )

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error during signup: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error during signup: {str(e)}")



@router.post("/google-auth", response_model=AuthResponse)
async def google_auth(form_data: GoogleAuthRequest ):
    """
    Google authentication endpoint for ID token verification.
    Supports both form data and JSON body.

    Args:
        form_data: OAuth2 form data with id_token and client_id
        auth_data: Google authentication data with ID token (JSON)

    Returns:
        Authentication response with JWT token
    """
    # Get data from form or JSON
    id_token = form_data.id_token
    client_id = form_data.client_id
    # Validate required fields
    if not id_token or not client_id:
        missing_fields = []
        if not id_token:
            missing_fields.append("id_token")
        if not client_id:
            missing_fields.append("client_id")

        raise HTTPException(
            status_code=400,
            detail=f"Missing required fields: {', '.join(missing_fields)}"
        )

    # Create auth request object
    google_auth_data = GoogleAuthRequest(id_token=id_token, client_id=client_id)
    return await process_google_auth(google_auth_data)


@router.get("/google-auth")
async def google_auth_redirect(client_id: str, redirect_uri: str, state: str = None):
    """
    Google authentication redirect endpoint.
    This endpoint initiates the OAuth flow by redirecting to Google's authorization page.

    Args:
        client_id: Tenant client ID
        redirect_uri: Redirect URI after authentication
        state: Optional state parameter for OAuth flow

    Returns:
        Redirect to Google's authorization page
    """
    # Log the request
    loggers.info(f"Google auth redirect requested for client_id: {client_id}, redirect_uri: {redirect_uri}")

    # Prepare the authorization URL
    try:
        # Try to parse the state as JSON if it exists
        if state:
            try:
                # First try to decode as base64
                try:
                    decoded_state = base64.b64decode(state.encode('utf-8')).decode('utf-8')
                    state_data = json.loads(decoded_state)
                    loggers.info(f"Successfully decoded base64 state: {state_data}")
                except Exception:
                    # If base64 decoding fails, try direct JSON parsing
                    try:
                        state_data = json.loads(state)
                    except json.JSONDecodeError:
                        # If it's not valid JSON, use it as is
                        loggers.warning(f"Invalid JSON in state parameter: {state}")
                        state_data = {"tenant_slug": client_id, "raw_state": state}
            except Exception as e:
                # If all parsing fails, use as is
                loggers.warning(f"Failed to parse state parameter: {str(e)}")
                state_data = {"tenant_slug": client_id, "raw_state": state}
        else:
            state_data = {"tenant_slug": client_id}

        # Add redirect_uri to state data so we can use it in the callback
        state_data["redirect_uri"] = redirect_uri
        loggers.info(f"State data for Google auth: {state_data}")
    except Exception as e:
        # Fallback to simple state if anything goes wrong
        loggers.error(f"Error processing state parameter: {str(e)}")
        state_data = {"tenant_slug": client_id, "redirect_uri": redirect_uri}

    params = {
        "client_id": GOOGLE_CLIENT_ID,
        "response_type": "code",
        "scope": "email openid profile",
        "redirect_uri": redirect_uri,
        "state": json.dumps(state_data),
        "prompt": "select_account",
    }

    # Redirect to Google's authorization page
    auth_url = f"{GOOGLE_AUTH_URL}?{urlencode(params)}"
    return RedirectResponse(url=auth_url)


@router.get("/oauth-config")
async def get_oauth_config():
    """
    Get OAuth configuration for frontend use.
    Returns the Google client ID needed for frontend authentication.

    Returns:
        Dictionary with OAuth configuration
    """
    return {
        "google_client_id": GOOGLE_CLIENT_ID,
        "redirect_uri": "http://localhost:3000/auth/google/callback"
    }

@router.get("/oauth-test")
async def test_oauth_config():
    """
    Test endpoint to check the Google OAuth configuration.

    Returns:
        Dictionary with OAuth configuration details for debugging
    """
    # Get all environment variables related to Google OAuth
    oauth_env = {
        "google_client_id": GOOGLE_CLIENT_ID,
        "google_client_secret": GOOGLE_CLIENT_SECRET[:4] + "..." if GOOGLE_CLIENT_SECRET else None,
        "google_auth_url": GOOGLE_AUTH_URL,
        "google_token_url": GOOGLE_TOKEN_URL,
        "google_userinfo_url": GOOGLE_USERINFO_URL,
        "is_client_id_set": GOOGLE_CLIENT_ID is not None and GOOGLE_CLIENT_ID != "",
        "is_client_secret_set": GOOGLE_CLIENT_SECRET is not None and GOOGLE_CLIENT_SECRET != ""
    }

    return {
        "oauth_config": oauth_env,
        "setup_instructions": "Make sure to configure your Google OAuth client ID and secret in your .env file. "
                             "Also ensure that the redirect URI is properly configured in the Google Cloud Console."
    }


@router.get("/google/callback")
async def google_callback(code: str, state: str = None):
    """
    Google OAuth callback endpoint.
    This endpoint handles the callback from Google's authorization page.

    Args:
        code: Authorization code from Google
        state: State parameter from the initial request

    Returns:
        Redirect to the frontend with the JWT token
    """
    try:
        # Parse the state parameter with better error handling
        try:
            if state:
                try:
                    # First try to decode as base64
                    try:
                        decoded_state = base64.b64decode(state.encode('utf-8')).decode('utf-8')
                        state_data = json.loads(decoded_state)
                        loggers.info(f"Successfully decoded base64 state in callback: {state_data}")
                    except Exception:
                        # If base64 decoding fails, try direct JSON parsing
                        try:
                            state_data = json.loads(state)
                        except json.JSONDecodeError:
                            # If it's not valid JSON, use it as is
                            loggers.warning(f"Invalid JSON in callback state parameter: {state}")
                            state_data = {"raw_state": state}
                except Exception as e:
                    # If all parsing fails, use as is
                    loggers.warning(f"Failed to parse callback state parameter: {str(e)}")
                    state_data = {"raw_state": state}
            else:
                state_data = {}

            loggers.info(f"Callback state data: {state_data}")
        except Exception as e:
            loggers.error(f"Error processing callback state parameter: {str(e)}")
            state_data = {}

        # Extract data from state with defaults
        tenant_slug = state_data.get("tenant_slug", "test")  # Default to "test" if not provided
        return_to = state_data.get("returnTo", "/dashboard")
        redirect_uri = state_data.get("redirect_uri", "http://localhost:3000/auth/google/callback")

        # Exchange the authorization code for an access token
        token_data = {
            "client_id": GOOGLE_CLIENT_ID,
            "client_secret": GOOGLE_CLIENT_SECRET,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": redirect_uri,
        }

        # Make a request to Google's token endpoint
        async with httpx.AsyncClient() as client:
            token_response = await client.post(GOOGLE_TOKEN_URL, data=token_data)
            token_json = token_response.json()

            if "error" in token_json:
                loggers.error(f"Error exchanging code for token: {token_json}")
                raise HTTPException(status_code=400, detail="Error exchanging code for token")

            # Get the ID token
            id_token = token_json.get("id_token")
            if not id_token:
                loggers.error("No ID token in response")
                raise HTTPException(status_code=400, detail="No ID token in response")

            # Create a GoogleAuthRequest object
            auth_data = GoogleAuthRequest(id_token=id_token, client_id=tenant_slug)

            # Process the Google authentication
            auth_response = await process_google_auth(auth_data)

            # Redirect to the frontend with the token
            frontend_url = f"http://localhost:3000{return_to}?token={auth_response.access_token}"
            return RedirectResponse(url=frontend_url)

    except Exception as e:
        loggers.error(f"Error during Google callback: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error during Google callback: {str(e)}")



async def process_google_auth(auth_data: GoogleAuthRequest):
    """
    Advanced Google authentication processor with optimized logic.
    Handles account linking, creation, and authentication in a streamlined manner.

    Args:
        auth_data: Google authentication data with ID token and client ID

    Returns:
        Authentication response with JWT token
    """
    try:
        # Input validation
        if not auth_data.id_token or not auth_data.client_id:
            error_msg = "Missing required fields" + (": ID token" if not auth_data.id_token else "") + (": client_id" if not auth_data.client_id else "")
            loggers.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)

        # Get Google user data and tenant info in parallel
        google_user_task = verify_google_token(auth_data.id_token)
        tenant_result_task = get_tenant_id_and_name_from_slug(auth_data.client_id)

        # Wait for both async operations to complete
        google_user, tenant_result = await asyncio.gather(google_user_task, tenant_result_task)

        # Extract tenant information
        tenant_id = str(tenant_result["_id"])
        tenant_name = tenant_result["name"]
        _, async_tenant_db = await get_db_from_tenant_id(tenant_id)

        # Build query to find user by either google_id or email
        query = {"$or": []}

        if "google_id" in google_user:
            query["$or"].append({"google_id": google_user["google_id"]})

        if "email" in google_user and google_user["email"]:
            query["$or"].append({"email": google_user["email"]})

        # If no search criteria available, we can't find or create proper user
        if not query["$or"]:
            loggers.error("Google account has no ID or email to identify user")
            raise HTTPException(status_code=400, detail="Google account missing required information")

        # Find existing user with single query
        user = await async_tenant_db.users.find_one(query) if query["$or"] else None

        update_fields = {}
        is_new_user = False

        if user:
            # User exists - handle account linking
            loggers.info(f"Found existing user: {user.get('username')} via {'google_id' if user.get('google_id') else 'email'}")

            # Store current login as previous login
            previous_login = user.get("last_login")
            if previous_login:
                update_fields["previous_login"] = previous_login

            # Always update last_login time
            update_fields["last_login"] = datetime.now()

            # Determine if we need to update the user record
            if not user.get("google_id") and "google_id" in google_user:
                # Link email account with Google
                update_fields["google_id"] = google_user["google_id"]
                update_fields["auth_provider"] = "both" if user.get("hashed_password") else "google"
                loggers.info(f"Linking existing account to Google ID: {google_user['google_id']}")

            # Update profile data if needed (only missing fields)
            if google_user.get("picture") and not user.get("profile_picture"):
                update_fields["profile_picture"] = google_user["picture"]

            if google_user.get("name") and not user.get("full_name"):
                update_fields["full_name"] = google_user["name"]

        else:
            # Create new user
            is_new_user = True
            loggers.info("Creating new user from Google account")

            # Get or create default role
            default_role_name = "agent"
            default_role = await async_tenant_db.roles.find_one({"name": default_role_name})

            if not default_role:
                default_role = {"name": default_role_name}
                role_id = await async_tenant_db.roles.insert_one(default_role)
                default_role["_id"] = role_id.inserted_id

            # Generate unique username
            base_username = google_user.get("email", "").split("@")[0] if google_user.get("email") else f"google_{google_user.get('google_id', '')[:8]}"
            username = base_username

            # Check for username uniqueness with efficient approach - try incrementing numbers if needed
            username_counter = 0
            while True:
                existing = await async_tenant_db.users.find_one(
                    {"username": username},
                    projection={"_id": 1}
                )
                if not existing:
                    break

                username_counter += 1
                username = f"{base_username}{username_counter}"

                # Safety check to avoid infinite loop
                if username_counter > 100:
                    username = f"{base_username}_{secrets.token_hex(4)}"
                    break

            # Create new user document
            user = {
                "username": username,
                "email": google_user.get("email"),
                "google_id": google_user.get("google_id"),
                "role": default_role["name"],
                "full_name": google_user.get("name"),
                "profile_picture": google_user.get("picture"),
                "auth_provider": "google",
                "created_at": datetime.now(),
                "last_login": datetime.now(),
                "phone_number": None,
                "country_code": None,
                "onboarding_completed": False
            }

            # Insert new user
            try:
                result = await async_tenant_db.users.insert_one(user)
                user["_id"] = result.inserted_id
                loggers.info(f"Created new Google user: {username}")
            except Exception as e:
                loggers.error(f"Failed to create new user: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Database error while creating user: {str(e)}")

        # Apply updates if there are any and it's not a brand new user
        if update_fields and not is_new_user:
            try:
                await async_tenant_db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": update_fields}
                )

                # Update the local user object with the new fields
                user.update(update_fields)
                loggers.info(f"Updated user {user['username']} with Google account data")
            except Exception as e:
                loggers.warning(f"Failed to update user data: {str(e)}")
                # Continue with authentication even if update fails

        # Create JWT token
        access_token = create_access_token(
            data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
            expires_delta=timedelta(hours=2)
        )

        # Create and return auth response
        auth_response = create_auth_response(
            user=user,
            access_token=access_token,
            tenant_id=tenant_id,
            tenant_name=tenant_name,
            tenant_slug=auth_data.client_id
        )

        loggers.info(f"Google authentication successful for user: {user['username']}")
        return auth_response

    except HTTPException as e:
        # Re-raise HTTP exceptions for proper status codes
        raise e
    except Exception as e:
        # Catch and log all other exceptions
        loggers.error(f"Error during Google authentication: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")

@router.post("/login", response_model=AuthResponse)
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends(), login_data: LoginRequest = None):
    """
    User login endpoint for email/password authentication.
    Supports both form data and JSON body.

    Args:
        form_data: OAuth2 form data with client ID
        login_data: User login data (JSON)

    Returns:
        Authentication response with JWT token
    """
    # Use form_data as the primary source or login_data as fallback for JSON requests
    username = form_data.username if form_data.username else (login_data.username if login_data else None)
    password = form_data.password if form_data.password else (login_data.password if login_data else None)
    client_id = form_data.client_id if form_data.client_id else (login_data.client_id if login_data else None)

    # Validate required fields
    if not username or not password or not client_id:
        missing_fields = []
        if not username:
            missing_fields.append("username")
        if not password:
            missing_fields.append("password")
        if not client_id:
            missing_fields.append("client_id")

        raise HTTPException(
            status_code=400,
            detail=f"Missing required fields: {', '.join(missing_fields)}"
        )

    try:
        # Get tenant information
        result = await get_tenant_id_and_name_from_slug(client_id)
        tenant_id = str(result["_id"])
        _, async_tenant_db = await get_db_from_tenant_id(tenant_id)

        # Single query to find user by username OR email (optimized)
        user = await async_tenant_db.users.find_one({
            "$or": [
                {"username": username},
                {"email": username}
            ]
        })

        if not user:
            loggers.error(f"User not found: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")

        # Check if user has a password
        if "hashed_password" not in user:
            # Check if they have Google auth
            if user.get("auth_provider") == "google":
                loggers.error(f"User has no password (Google-only account): {username}")
                raise HTTPException(
                    status_code=401,
                    detail="This account uses Google authentication only. Please sign in with Google."
                )
            else:
                loggers.error(f"User has no password and invalid auth provider: {username}")
                raise HTTPException(status_code=401, detail="Invalid account configuration. Contact support.")

        # Verify password
        if not verify_password(password, user["hashed_password"]):
            loggers.error(f"Incorrect password: {username}")
            raise HTTPException(status_code=401, detail="Invalid username or password")

        # Store current login time as previous login time
        previous_login = user.get("last_login")

        # Update login times (non-blocking for performance)
        try:
            update_data = {
                "previous_login": previous_login,
                "last_login": datetime.now()
            }

            # Fire and forget update for better performance
            asyncio.create_task(
                async_tenant_db.users.update_one(
                    {"_id": user["_id"]},
                    {"$set": update_data}
                )
            )

            # Update user object with new login times
            user["previous_login"] = previous_login
            user["last_login"] = datetime.now()

        except Exception:
            # Don't block login for login time update failures
            pass

        # Create JWT token with consistent expiration time (2 hours)
        try:
            access_token = create_access_token(
                data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
                expires_delta=timedelta(hours=2)
            )
        except Exception as e:
            loggers.error(f"Error generating access token: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to generate authentication token")

        # Create response without password (optimized)
        user_without_password = {k: v for k, v in user.items() if k != "hashed_password"}
        user_without_password = convert_objectid_to_str(user_without_password)

        # Create standardized auth response
        auth_response = create_auth_response(
            user=user,
            access_token=access_token,
            tenant_id=tenant_id,
            tenant_name=result["name"],
            tenant_slug=client_id
        )

        return auth_response

    except HTTPException as e:
        # Re-raise HTTP exceptions
        loggers.error(f"HTTPException during login: {e.detail}")
        raise e
    except Exception as e:
        # Log and raise other exceptions
        loggers.error(f"Error during login: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error during login: {str(e)}")

@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    """
    Get tenant ID from slug.

    Args:
        slug: The tenant slug

    Returns:
        Dictionary with tenant ID and name
    """
    try:
        result = await get_tenant_id_and_name_from_slug(slug)
        tenant_id = str(result["_id"])
        tenant_name = result["name"]

        return{
            "tenant_id": tenant_id,
            "tenant_name": tenant_name
        }
    except HTTPException as e:
        # Re-raise the HTTPException from get_tenant_id_and_name_from_slug
        loggers.error(f"Error getting tenant ID from slug: {e.detail}")
        raise e
    except Exception as e:
        # Log the error and raise a proper HTTPException
        loggers.error(f"Unexpected error getting tenant ID from slug: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing tenant slug: {str(e)}")


@router.get("/verify_token")
async def verify_token(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Verify JWT token and return user details.
    If the token is invalid, get_tenant_info will raise an exception.

    Args:
        current_user: User tenant information from token

    Returns:
        Dictionary with user details
    """
    # Get user details without password
    user_data = {
        "id": str(current_user.user.id),
        "username": current_user.user.username,
        "role": current_user.user.role.name if hasattr(current_user.user.role, 'name') else current_user.user.role,
        "tenant_id": current_user.tenant_id,
        "email": getattr(current_user.user, "email", None),
        "full_name": getattr(current_user.user, "full_name", None),
        "profile_picture": getattr(current_user.user, "profile_picture", None),
        "auth_provider": getattr(current_user.user, "auth_provider", "password")
    }
    return user_data


@router.post("/users/invite")
async def invite_agent(
    invitation: AgentInvitation,
    current_user: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Invite a new agent by generating a registration link with a token.
    Only admins can invite agents.

    Args:
        invitation: Agent invitation details
        current_user: Current user information

    Returns:
        Dictionary with registration token
    """
    tenant_id = current_user.tenant_id
    users_collection = current_user.async_db.users
    invited_by = str(tenant_id)

    existing_user = await users_collection.find_one({"username": invitation.username})
    if existing_user:
        return {"registration_token": None, "success": False, "msg": "Username already exists"}

    invitations_collection = current_user.async_db.invitations
    existing_invitation = await invitations_collection.find_one({"username": invitation.username})
    if existing_invitation:
        try:
            result = await invitations_collection.delete_many({"username": invitation.username})
            if result.deleted_count > 0:
                loggers.info(f"Record with username '{invitation.username}' existed, therefore deleted existing invitation to create new one.")
        except HTTPException as e:
            loggers.error(e)
            raise e

    # Create invitation token
    token = create_invitation_token(
        username=invitation.username,
        role=invitation.role,
        invited_by=invited_by,
        tenant_id=tenant_id,
        expires_delta=timedelta(days=7)  # Valid for 7 days
    )

    invitation_record = {
        "username": invitation.username,
        "token": token,
        "role": invitation.role,
        "invited_by": invited_by,
        "expires_at": datetime.now() + timedelta(days=7),
        "used": False,
    }

    await invitations_collection.insert_one(invitation_record)

    return {"registration_token": token, "success": True, "msg": "Token Generated!"}

@router.post("/users/register")
async def register_agent(registration: AgentRegistration):
    """
    Register a new agent using the invitation token.

    Args:
        registration: Agent registration details

    Returns:
        Dictionary with registration status
    """
    # Verify the token and extract the agent's name
    try:
        agent_username, invited_by, role_ = await verify_invitation_token(registration.token)
    except HTTPException as e:
        loggers.error(e)
        raise e

    _, async_tenant_database = await get_db_from_tenant_id(invited_by)
    invitations_collection = async_tenant_database.invitations
    invitation = await invitations_collection.find_one({"token": registration.token})

    if not invitation:
        return {"msg": "Invalid invitation token!", "success": False}
    if invitation.get("used"):
        return {"msg": "Invitation token has already been used. Request the supervisor to generate a new one!", "success": False}

    users_collection = async_tenant_database.users
    # Check if the desired username already exists
    existing_user = await users_collection.find_one({"username": agent_username})
    if existing_user:
        return {"msg": "Username already exists!", "success": False}

    # Hash the provided password
    hashed_password = ph.hash(registration.password)

    # Create the new agent user
    new_agent = {
        "username": registration.username,
        "hashed_password": hashed_password,
        "role": role_,
        "created_by": invited_by,
        "created_at": datetime.now(),
        "onboarding_completed": False
    }

    # Insert the new agent into the database
    result = await users_collection.insert_one(new_agent)
    new_agent["_id"] = result.inserted_id

    await invitations_collection.update_one(
        {"token": registration.token},
        {"$set": {"used": True}}
    )

    return {"msg": "Agent registered successfully", "success": True}

@router.post("/users/change_password")
async def change_password(
    req: ChangePasswordRequest,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    """
    Change user password.

    Args:
        req: Change password request
        user_tenant_info: User tenant information

    Returns:
        Dictionary with status message
    """
    users_collection = user_tenant_info.async_db.users

    user = await users_collection.find_one({"_id": ObjectId(user_tenant_info.user.id)})
    if not user:
        raise HTTPException(
            status_code=404,
            detail="User not found."
        )

    if not verify_password(req.old_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="Current password is incorrect."
        )

    if verify_password(req.new_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="New password must be different from current password"
        )

    new_hashed_password = ph.hash(req.new_password)
    result = await users_collection.update_one(
        {"username": user["username"]},
        {"$set": {"hashed_password": new_hashed_password}}
    )

    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to update password."
        )

    loggers.info(f"Password successfully changed for user: {user['username']}")
    return {"message": "Password successfully changed."}

@router.post("/users/reset_password")
async def reset_password(
    req: ResetPasswordRequest,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Reset user password (admin only).

    Args:
        req: Reset password request
        user_tenant_info: User tenant information

    Returns:
        Dictionary with status message and new password
    """
    users_collection = user_tenant_info.async_db.users

    user = await users_collection.find_one({"username": req.username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")

    new_random_password = ''.join(secrets.choice('0123456789abcdef') for _ in range(10))
    new_hashed_password = ph.hash(new_random_password)

    result = await users_collection.update_one(
        {"username": req.username},
        {"$set": {"hashed_password": new_hashed_password}}
    )

    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to reset password."
        )

    loggers.info(f"Password reset successfully for user: {req.username}")
    return {"message": f"Password reset to {new_random_password} successfully."}

@router.get("/users/{user_id}")
async def get_user_by_id(
    user_id: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Get user details by user ID (requires admin role).

    Args:
        user_id: User ID
        user_tenant_info: User tenant information

    Returns:
        User details
    """
    try:
        # Get users collection from tenant database
        users_collection = user_tenant_info.async_db.users

        # Find user by ID
        user = await users_collection.find_one({"_id": ObjectId(user_id)}, {"username": 1, "role": 1})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Return only name and role information
        return serialize_mongo_doc(user)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching user details: {str(e)}")


@router.post("/onboarding", response_model=OnboardingResponse)
async def user_onboarding(
    onboarding_data: OnboardingRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    User onboarding endpoint for profile setup after signup.

    This endpoint handles user profile setup immediately after successful signup.
    It uses the existing JWT auth token from the signup response.

    Args:
        onboarding_data: Onboarding form data (age, difficulty, topics)
        current_user: Current user information from JWT token

    Returns:
        OnboardingResponse with completion status and personalization info
    """
    try:
        user_id = str(current_user.user.id)
        loggers.info(f"Processing onboarding for user {user_id}")

        # Inserting
        await current_user.async_db.users.find_one_and_update(
            {"_id": ObjectId(user_id), "onboarding_completed": False},
            {"$set": {
                "age": onboarding_data.age,
                "difficulty_level": onboarding_data.difficulty_level,
                "preferred_topics": onboarding_data.preferred_topics,
                "onboarding_completed": True
            }}
        )

        response = OnboardingResponse(
            success=True,
            message="Onboarding completed successfully",
            user_id=user_id,
            personalization_ready=True
        )

        loggers.info(f"Onboarding completed successfully for user {user_id}")
        return response

    except ValueError as e:
        loggers.error(f"Validation error during onboarding: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        loggers.error(f"Error during onboarding: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error during onboarding: {str(e)}")

