# Retry Endpoints Documentation

## Overview
Two new retry endpoints have been added to allow users to reset and reattempt tasks:

1. **Task Set Retry**: Reset entire task set and all its tasks
2. **Task Item Retry**: Reset individual task item

## Multiple Attempts Support
- ✅ **Removed single attempt limitation** - tasks can now be attempted multiple times
- ✅ **Score tracking** - only first attempt counts toward task set scores
- ✅ **Proper calculation** - task set scores are updated correctly when individual items are reset

## Endpoints

### 1. Task Set Retry
**Endpoint:** `POST /v1/management/task-sets/retry/{task_set_id}`

**Description:** Reset entire task set and all its task items to start fresh.

**What it does:**
- Resets ALL task items in the task set (scores, answers, completion status)
- Resets the task set scores and status
- Allows complete retry of the entire task set

**Request:**
```http
POST /v1/management/task-sets/retry/507f1f77bcf86cd799439011
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Task set reset successfully",
    "task_set_id": "507f1f77bcf86cd799439011",
    "tasks_reset": 8,
    "total_tasks": 8,
    "reset_at": "2024-01-15T10:30:00.000Z"
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

**Reset Fields for Task Set:**
- `scored`: 0
- `attempted_tasks`: 0
- `status`: "pending"
- `completed_at`: null
- `remark`: "Task set reset for retry"

**Reset Fields for All Task Items:**
- `scored`: 0
- `user_answer`: null
- `answered_at`: null
- `is_attempted`: false
- `submitted`: false
- `submitted_at`: null
- `attempts_count`: 0
- `status`: "not_attempted"
- `result`: null
- `remark`: null

### 2. Task Item Retry
**Endpoint:** `POST /v1/management/task-items/retry/{task_item_id}`

**Description:** Reset individual task item and update task set scores accordingly.

**What it does:**
- Resets the specific task item (score, answer, completion status)
- Subtracts the old score from the task set total
- Decreases attempted tasks count in task set
- Allows retry of just that specific task

**Request:**
```http
POST /v1/management/task-items/retry/507f1f77bcf86cd799439012
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Task item reset successfully",
    "task_item_id": "507f1f77bcf86cd799439012",
    "old_score": 10,
    "was_submitted": true,
    "reset_at": "2024-01-15T10:30:00.000Z"
  },
  "meta": {
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

**Task Set Score Adjustment:**
- If task was previously submitted with score > 0:
  - Subtracts old score from task set total
  - Decreases `attempted_tasks` count by 1
  - Updates task set `updated_at` timestamp

## Multiple Attempts Logic

### Current Implementation
The system now supports unlimited attempts with proper score tracking:

1. **First Attempt**: 
   - Counts toward task set scores
   - Sets `submitted: true` and `submitted_at`
   - Increments task set `attempted_tasks`

2. **Subsequent Attempts**:
   - Updates task item scores and feedback
   - Does NOT affect task set scores (prevents score inflation)
   - Increments `attempts_count` on task item
   - Allows score improvement for individual tracking

3. **Retry Reset**:
   - Completely resets attempt tracking
   - Next submission becomes "first attempt" again
   - Proper score calculation maintained

### Score Calculation Flow

**Task Set Retry:**
```
1. Reset all task items → scored: 0, attempts_count: 0, submitted: false
2. Reset task set → scored: 0, attempted_tasks: 0
3. Next submissions count as first attempts
```

**Task Item Retry:**
```
1. Get current task item score (e.g., 10 points)
2. Reset task item → scored: 0, attempts_count: 0, submitted: false
3. Update task set → subtract 10 from total, decrease attempted_tasks by 1
4. Next submission counts as first attempt
```

## Error Handling

### Task Set Retry Errors:
- `400`: Invalid task set ID format
- `403`: User doesn't own the task set
- `404`: Task set not found
- `500`: Database update failed

### Task Item Retry Errors:
- `400`: Invalid task item ID format
- `404`: Task item not found
- `500`: Database update failed

## Security
- ✅ **Ownership validation** - users can only retry their own task sets
- ✅ **JWT authentication** required for all endpoints
- ✅ **Input validation** - validates ObjectId format

## Frontend Integration

### Task Set Retry
```javascript
const retryTaskSet = async (taskSetId) => {
  const response = await fetch(`/v1/management/task-sets/retry/${taskSetId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (response.ok) {
    const result = await response.json();
    console.log(`Reset ${result.data.tasks_reset} tasks`);
    // Refresh task set data
    await loadTaskSet(taskSetId);
  }
};
```

### Task Item Retry
```javascript
const retryTaskItem = async (taskItemId) => {
  const response = await fetch(`/v1/management/task-items/retry/${taskItemId}`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  if (response.ok) {
    const result = await response.json();
    console.log(`Reset task, old score: ${result.data.old_score}`);
    // Refresh task item and task set data
    await loadTaskItem(taskItemId);
    await loadTaskSetScores(taskSetId);
  }
};
```

## Database Changes
No schema changes required - uses existing fields and proper reset logic.

## Testing
Test both endpoints with:
1. Valid task set/item IDs
2. Invalid IDs (should return 400/404)
3. Other user's tasks (should return 403)
4. Score calculation verification after retry
