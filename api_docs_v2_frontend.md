# Nepali App V2 API Documentation for Frontend

## Overview
V2 maintains the **exact same Socket.IO flow and data structures** as V1. The only differences are:
- Endpoint URLs change from `/v1/` to `/v2/`
- Backend has performance optimizations (transparent to frontend)
- Same events, same data format, same responses
- **Same task set structure**: `tasks: ["id1", "id2"]` (simple array of IDs)

## V2 Socket.IO Flow (Same as V1)

### 1. Socket Authentication
**Endpoint:** `POST /v2/connect`

**Description:** Create authenticated Socket.IO session (same as V1).

**Request:**
```http
POST /v2/connect
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "session_token": "token_v2_abc123def456",
  "session_id": "session_v2_abc123",
  "websocket_url": "/v2/socket.io",
  "expires_at": "2024-01-15T12:30:00Z",
  "status": "ready",
  "service_version": "v2",
  "instructions": {
    "next_step": "Connect to WebSocket using session_token",
    "websocket_endpoint": "/v2/socket.io",
    "flow": {
      "1": "Send 'stream_starting' event",
      "2": "Wait for 'stream_starting_ack' response",
      "3": "Send binary audio chunks",
      "4": "Send 'stream_completed' or 'stream_stop' to finish"
    }
  }
}
```

### 2. WebSocket Connection
**Endpoint:** `/v2/socket.io`

**Description:** Same Socket.IO connection as V1, just different URL.

### 3. Socket Events (Identical to V1)

#### stream_starting
**Event:** `stream_starting`
**Data:** `{ session_id: "session_v2_abc123" }`
**Response:** `stream_starting_ack`

#### binary_data
**Event:** `binary_data`
**Data:** `<audio_chunk_binary_data>`
**Metadata:** `{ session_id: "session_v2_abc123", chunk_index: 1 }`

#### stream_completed
**Event:** `stream_completed`
**Data:** `{ session_id: "session_v2_abc123" }`
**Response:** Same task and story data as V1

#### stream_stop
**Event:** `stream_stop`
**Data:** `{ session_id: "session_v2_abc123" }`
**Response:** `stream_stop_ack`

## Frontend Integration (V1 vs V2)

### V1 Frontend Code
```javascript
// V1 Socket connection
const socket = io('/v1/socket/socket.io', {
  auth: { session_token: sessionToken }
});

// V1 Authentication
const authResponse = await fetch('/v1/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### V2 Frontend Code (Only URL Changes)
```javascript
// V2 Socket connection - SAME EVENTS, SAME DATA
const socket = io('/v2/socket.io', {
  auth: { session_token: sessionToken }
});

// V2 Authentication - SAME REQUEST/RESPONSE FORMAT
const authResponse = await fetch('/v2/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### Socket Events (Identical for V1 and V2)
```javascript
// Start streaming (same for both V1 and V2)
socket.emit('stream_starting', { session_id: sessionId });

// Send audio chunks (same for both V1 and V2)
socket.emit('binary_data', audioChunk, {
  session_id: sessionId,
  chunk_index: chunkIndex
});

// Complete streaming (same for both V1 and V2)
socket.emit('stream_completed', { session_id: sessionId });

// Listen for responses (same for both V1 and V2)
socket.on('stream_starting_ack', (data) => {
  console.log('Streaming acknowledged:', data);
});

socket.on('task_generation_complete', (data) => {
  // Same response format for both V1 and V2
  console.log('Tasks generated:', data);
});
```

## HTTP Audio Processing Alternative 

### V2 HTTP Endpoint (Alternative to Socket)
**Endpoint:** `POST /v2/audio/process`

**Request:**
```http
POST /v2/audio/process
Content-Type: multipart/form-data
Authorization: Bearer <jwt_token>

audio_file: <audio_file>
```

**Response:**
```json
{
  "status": "success",
  "task_set_id": "507f1f77bcf86cd799439011",
  "story_set_id": "507f1f77bcf86cd799439012",
  "session_id": "session_v2_abc123",
  "processing_time": 45.2
}
```

## Management Service Endpoints (Same for V1 & V2)

### 2. Get Task Set with Stories
**Endpoint:** `GET /api/v1/management/task-sets/{task_set_id}`

**Description:** Get task set details with optional tasks and stories included.

**Parameters:**
- `task_set_id` (path): The task set ID from audio processing
- `include_tasks` (query, optional): Include tasks in response (default: false) keep it false
- `include_stories` (query, optional): Include stories in response (default: false) keep it false
- `fields` (query, optional): Specific fields to retrieve

**Request:**
```http
GET /api/v1/management/task-sets/507f1f77bcf86cd799439011?include_tasks=true&include_stories=true
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "user_id": "user123",
  "input_type": "audio",
  "status": "completed",
  "total_tasks": 8,
  "difficulty_level": 2,
  "created_at": "2024-01-15T10:30:00Z",
  "tasks": [
    {
      "_id": "task_id_1",
      "type": "single_choice",
      "title": "Task Title",
      "question": {
        "text": "Question text",
        "metadata": {
          "object_name": "task_images/task_123.jpg",
          "content_type": "image/jpeg"
        }
      },
      "status": "not_attempted"
    }
  ],
  "stories": [
    {
      "_id": "story_id_1",
      "steps": [
        {
          "step_number": 1,
          "content": "Story content",
          "metadata": {
            "object_name": "story_images/story_123.jpg",
            "content_type": "image/jpeg"
          }
        }
      ],
      "total_steps": 5,
      "status": "active"
    }
  ]
}
```

### 3. Get Task Items by Set ID
**Endpoint:** `GET /api/v1/management/task-items/set/{set_id}/tasks`

**Description:** Get all task items for a specific task set.

**Parameters:**
- `set_id` (path): The task set ID
- `fields` (query, optional): Fields to retrieve

**Request:**
```http
GET /api/v1/management/task-items/set/507f1f77bcf86cd799439011/tasks?fields=type,title,question,status
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "tasks": [
    {
      "_id": "task_id_1",
      "type": "single_choice",
      "title": "Identify the Object",
      "question": {
        "text": "What is shown in the image?",
        "metadata": {
          "object_name": "task_images/task_123.jpg",
          "content_type": "image/jpeg",
          "folder": "task_images"
        }
      },
      "status": "not_attempted"
    },
    {
      "_id": "task_id_2", 
      "type": "speak_word",
      "title": "Pronunciation Practice",
      "question": {
        "text": "Pronounce this word",
        "metadata": {
          "object_name": "task_audio/task_124.mp3",
          "content_type": "audio/mpeg",
          "folder": "task_audio"
        }
      },
      "status": "not_attempted"
    }
  ],
  "total_count": 8
}
```

### 4. Get Single Task Item
**Endpoint:** `GET /api/v1/management/task-items/{task_id}`

**Description:** Get a specific task item by ID.

**Parameters:**
- `task_id` (path): The task ID
- `fields` (query, optional): Fields to retrieve

**Request:**
```http
GET /api/v1/management/task-items/task_id_1?fields=type,title,question,correct_answer,choices
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "_id": "task_id_1",
  "type": "single_choice",
  "title": "Identify the Object",
  "question": {
    "text": "What is shown in the image?",
    "metadata": {
      "object_name": "task_images/task_123.jpg",
      "content_type": "image/jpeg",
      "folder": "task_images",
      "url": "https://presigned-url-for-image"
    }
  },
  "correct_answer": "A",
  "choices": ["Apple", "Orange", "Banana", "Grape"]
}
```

### 5. Get Story IDs by Task Set
**Endpoint:** `GET /api/v1/management/story/set/{task_set_id}/stories`

**Description:** Get list of story IDs for a task set.

**Request:**
```http
GET /api/v1/management/story/set/507f1f77bcf86cd799439011/stories
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "story_ids": [
    "story_id_1",
    "story_id_2"
  ],
  "total_count": 2
}
```

### 6. Get Single Story
**Endpoint:** `GET /api/v1/management/story/{story_id}`

**Description:** Get a specific story by ID.

**Parameters:**
- `story_id` (path): The story ID
- `fields` (query, optional): Fields to retrieve

**Request:**
```http
GET /api/v1/management/story/story_id_1?fields=steps,total_steps,status
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "_id": "story_id_1",
  "steps": [
    {
      "step_number": 1,
      "content": "Once upon a time...",
      "metadata": {
        "object_name": "story_images/story_123.jpg",
        "content_type": "image/jpeg",
        "folder": "story_images"
      }
    },
    {
      "step_number": 2,
      "content": "The adventure continues...",
      "metadata": {
        "object_name": "story_audio/story_124.mp3",
        "content_type": "audio/mpeg", 
        "folder": "story_audio"
      }
    }
  ],
  "total_steps": 5,
  "completed_steps": 0,
  "status": "active"
}
```

## Complete Frontend Migration Guide

### For Existing V1 Frontend Implementation

**Step 1: Update URLs Only**
```javascript
// Change these URLs from V1 to V2:
// OLD: '/v1/connect' → NEW: '/v2/connect'
// OLD: '/v1/socket/socket.io' → NEW: '/v2/socket.io'

// Everything else stays exactly the same!
```

**Step 2: Socket Connection (No Logic Changes)**
```javascript
// V1 Code (existing)
const socket = io('/v1/socket/socket.io', {
  auth: { session_token: sessionToken }
});

// V2 Code (just change URL)
const socket = io('/v2/socket.io', {
  auth: { session_token: sessionToken }
});

// All events remain identical:
socket.emit('stream_starting', { session_id });
socket.emit('binary_data', audioChunk, { session_id, chunk_index });
socket.emit('stream_completed', { session_id });
```

**Step 3: Authentication (No Logic Changes)**
```javascript
// V1 Code (existing)
const response = await fetch('/v1/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});

// V2 Code (just change URL)
const response = await fetch('/v2/connect', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## Summary: V1 vs V2 for Frontend

| Aspect | V1 | V2 | Change Required |
|--------|----|----|-----------------|
| Socket Events | `stream_starting`, `binary_data`, `stream_completed` | `stream_starting`, `binary_data`, `stream_completed` | **None** |
| Event Data Format | `{ session_id, chunk_index }` | `{ session_id, chunk_index }` | **None** |
| Response Format | Same task/story objects | Same task/story objects | **None** |
| Authentication | `POST /v1/connect` | `POST /v2/connect` | **URL only** |
| WebSocket URL | `/v1/socket/socket.io` | `/v2/socket.io` | **URL only** |
| Management APIs | Same endpoints | Same endpoints | **None** |

**Bottom Line:** Frontend developers only need to change 2 URLs. All existing V1 socket logic works unchanged with V2.
